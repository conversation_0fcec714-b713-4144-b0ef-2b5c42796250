import VxeUI from 'vxe-pc-ui'
import 'vxe-pc-ui/lib/style.css'
import { number2 } from '.'
import CopyBtn from '@/components/CopyBtn/index.vue'
import { h, withDirectives } from 'vue'
import { vCopy } from '@/directive/copy'

VxeUI.formats.add('number', {
  tableCellFormatMethod: ({ cellValue }) => number2(cellValue, 2),
  tableFooterCellFormatMethod: ({ itemValue }) => number2(itemValue, 2),
})

/** 用于在单元格复制按钮操作 */
VxeUI.renderer.add('copy', {
  renderTableDefault: (_renderOpts, renderParams) => {
    const value = renderParams.row[renderParams.column.field]
    return h('div', {}, [h('span', {}, value || '--'), value && withDirectives(h(CopyBtn), [[vCopy, String(value)]])])
  },
})
export default VxeUI
