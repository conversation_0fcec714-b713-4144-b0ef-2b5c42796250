<template>
  <div class="main">
    <Form v-model:form="formArr" :page-type="PageType.PLATFORM_SUB_CATEGORIES" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.PLATFORM_SUB_CATEGORIES" :get-list="GetList">
      <template #left-btn>
        <a-button :disabled="!btnPermission[82006]" type="primary" @click="tapManipulate('add')">新建小类</a-button>
      </template>
      <template #brand_authorization="{ row }">
        <span>{{ row.brand_authorization || '--' }}</span>
      </template>
      <template #status="{ row }">
        <a-switch
          :disabled="!btnPermission['82007']"
          class="btn"
          @click="tapSwitch($event, row, null)"
          v-model:checked="[false, true][row.status]"
          checked-children="启用"
          un-checked-children="停用"
        />
      </template>
      <template #create_at="{ row }">
        <span>{{ row.create_at ? row.create_at.slice(0, 16) : '' }}</span>
      </template>
      <template #update_at="{ row }">
        <span>{{ row.update_at ? row.update_at.slice(0, 16) : '' }}</span>
      </template>
      <template #register_designated_site="{ row }">
        <span>{{ registerDesignatedSite(row.register_designated_site) }}</span>
      </template>
      <template #operate="{ row }">
        <a-button :disabled="!btnPermission[82002]" @click="detail(row)" class="mr-10px">查看</a-button>
        <a-button :disabled="!btnPermission[82003]" class="mr-10px" @click="tapManipulate('compiler', row)">编辑</a-button>
        <a-button :disabled="!btnPermission[82004]" @click="tapManipulate('removes', row)">删除</a-button>
      </template>
    </BaseTable>

    <!-- 查看 -->
    <edit-drawer ref="editDrawerRef" @updateStatus="editUpdateStatus" @success="saveSuccess" />
    <detail-drawer ref="detailDrawerRef" />
    <a-modal :zIndex="5000" v-model:open="visibleData.isShow" :title="visibleData.title">
      <div class="modalContent">{{ visibleData.content }}</div>
      <template #footer>
        <a-button id="roleManagementFormComfirm" v-if="visibleData.isConfirmBtn" style="margin-right: 0.8333rem" type="primary" @click="visibleData.okFn" :danger="visibleData.okType === 'danger'">
          {{ visibleData.confirmBtnText }}
        </a-button>
        <a-button id="roleManagementFormCancel" v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
      </template>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import Form from '@/components/Form.vue'
import { PageType } from '@/common/enum'
import { Delete, GetList, UpdateStatus } from '@/servers/platformSubCateg'
import { GetCategoryOptions } from '@/servers/StoreLists'
import { message } from 'ant-design-vue'
import { onMounted } from 'vue'
import { GetEnum } from '@/servers/Common'
import DetailDrawer from './components/DetailDrawer.vue'
import EditDrawer from './components/EditDrawer.vue'

const { btnPermission } = usePermission()

const editDrawerRef = ref()
const detailDrawerRef = ref()
const oldStatus = ref(null)
const visibleData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {
    visibleData.isShow = false
  },
})
const registerSiteArr = ref([]) as any
const enumData = ref()
const formArr: any = ref([
  {
    label: '请选择平台大类',
    value: null,
    type: 'select_one',
    selectArr: [],
    key: 'platform_category_id',
    search: true,
  },
  {
    label: '搜索平台小类名称',
    value: null,
    type: 'input',
    key: 'name',
  },
  {
    label: '品牌授权是否可选',
    value: null,
    type: 'select',
    selectArr: [
      { label: '是', value: 1 },
      { label: '否', value: 0 },
    ],
    key: 'brand_authorization',
  },
  {
    label: '证件类型',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'document_type',
  },
  {
    label: '保证金',
    value: null,
    type: 'select',
    selectArr: [
      { label: '是', value: 1 },
      { label: '否', value: 0 },
    ],
    key: 'is_margin',
  },
  {
    label: '状态',
    value: null,
    type: 'select',
    selectArr: [
      { label: '启用', value: 1 },
      { label: '停用', value: 0 },
    ],
    key: 'status',
  },
  {
    label: '注册指定站点',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'register_designated_site',
  },
])
const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.PLATFORM_SUB_CATEGORIES) {
    const arr: any[] = []
    obj.PLATFORM_SUB_CATEGORIES.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}
onMounted(() => {
  search()
  initScreening()
  getEnum()
  GetCategoryOptions(null).then((res) => {
    formArr.value.forEach((item) => {
      if (item.key == 'platform_category_id') {
        item.selectArr = res.data.map((x) => {
          return { label: x.name, value: x.id }
        })
      }
    })
  })
})

// 获取所有枚举选项
const getEnum = () => {
  GetEnum().then((res) => {
    enumData.value = res.data
    formArr.value.forEach((item) => {
      if (item.key == 'document_type') {
        item.selectArr = enumData.value.data.document_type
      }
      if (item.key == 'register_designated_site') {
        item.selectArr = enumData.value.data.register_designated_site
        registerSiteArr.value = enumData.value.data.register_designated_site
      }
    })
  })
}

const tapManipulate = (type: string, row: any = '') => {
  switch (type) {
    case 'add':
      editDrawerRef.value?.open(null, 'add')
      break
    case 'compiler':
      oldStatus.value = row.status
      editDrawerRef.value?.open(row.id, 'edit')
      break
    case 'removes':
      visibleData.isShow = true
      visibleData.title = '删除平台小类'
      visibleData.content = `即将删除该平台小类，删除后：
  - 店铺信息维护时将无法选择当前小类
  - 无法申请该平台小类开通新店铺

  确定要删除该平台小类吗？`
      visibleData.confirmBtnText = '确定'
      visibleData.isCancelBtn = true
      visibleData.okType = 'danger'
      visibleData.okFn = () => {
        deleteRole(row.id)
      }
      break
    default:
      break
  }
}
const tapSwitch = (e, row, fn) => {
  const okFn =
    fn ||
    function nameedFn() {
      updateRoleStatus({ id: row.id, status: e ? 1 : 0 })
    }
  const oldStatus = row.status
  if (!oldStatus) {
    okFn()
  } else {
    visibleData.isShow = true
    visibleData.title = '停用平台小类'
    visibleData.content = `即将停用该平台小类，停用后：
  - 店铺信息维护时将无法选择当前小类
  - 无法申请该平台小类开通新店铺

  确定要停用该平台小类吗？`
    visibleData.confirmBtnText = '确定'
    visibleData.okType = 'danger'
    visibleData.isCancelBtn = true
    visibleData.okFn = () => {
      okFn()
      visibleData.isShow = false
    }
  }
}

// 详情
const detail = (item) => {
  detailDrawerRef.value?.open(item.id, btnPermission.value[82005])
}

const tableRef = ref()
const search = () => tableRef.value.search()
// const showTableSetting = () => tableRef.value.showTableSetting()

// 平台小类启用停用
const updateRoleStatus = (obj) => {
  UpdateStatus(obj).then(() => {
    tableRef.value.search()
    visibleData.isShow = false
  })
}
// 删除平台小类
const deleteRole = (id) => {
  Delete({ id })
    .then(() => {
      visibleData.isShow = false
      message.success('删除成功')
      search()
    })
    .catch(() => {
      visibleData.isShow = false
    })
}
const editUpdateStatus = (obj) => {
  tapSwitch(null, obj, obj.fn)
}
const saveSuccess = () => {
  search()
}

const registerDesignatedSite = (val) => {
  return registerSiteArr.value.find((n) => n.value == val).label || '--'
}
</script>
<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .breadcrumbBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 2.5rem;
    margin: 0.6667rem 0;

    .title {
      font-size: 1.3333rem;
      font-weight: bold;
      color: #000;
    }
  }

  .btnlist {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    margin-top: 1.25rem;
  }

  .formBox {
    .operatingAreaBox {
      display: flex;
      align-items: center;
      height: 2.6667rem;

      .tag {
        padding: 0 0.8333rem;
        cursor: pointer;
        user-select: none;
      }

      .btn {
        margin-right: 0.8333rem;
      }
    }
  }


}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 8.3333rem;
    min-width: 8.3333rem;
    margin-right: 2.5rem;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}

.tableBtn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 2.6667rem;
  margin-top: 0.8333rem;
  margin-bottom: 0.8333rem;
  font-size: 1.3333rem;
  font-weight: bold;
  color: #000;
}

.vxe-icon {
  width: 1.3333rem;
  height: 1.3333rem;
}
</style>
