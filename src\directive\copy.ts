// 复制文本到剪贴板的指令
import type { Directive, DirectiveBinding } from 'vue'
import { message } from 'ant-design-vue'

export const vCopy: Directive = {
  mounted(el: any, binding: DirectiveBinding) {
    const { value } = binding

    const copyHandler = async (e: Event) => {
      e.preventDefault()
      e.stopPropagation()

      let textToCopy = ''

      // 获取要复制的文本
      if (typeof value === 'string') {
        textToCopy = value
      } else if (typeof value === 'function') {
        textToCopy = value()
      } else {
        // 如果没有指定文本，尝试从元素内容获取
        textToCopy = el.textContent || el.innerText || ''
      }

      if (!textToCopy) {
        return
      }

      try {
        // 使用现代 Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
          await navigator.clipboard.writeText(textToCopy)
        } else {
          const input = document.createElement('input')
          input.value = textToCopy
          document.body.appendChild(input)
          input.focus()
          input.select()

          document.execCommand('copy')
          document.body.removeChild(input)
        }
        message.success('复制成功')
      } catch (error) {
        message.error('复制失败，请手动复制')
      }
    }

    // 保存处理函数引用
    el._copyHandler = copyHandler

    // 添加点击事件监听
    el.addEventListener('click', copyHandler)

    // 添加鼠标样式
    el.style.cursor = 'pointer'
  },

  beforeUnmount(el: any) {
    // 清理事件监听器
    if (el._copyHandler) {
      el.removeEventListener('click', el._copyHandler)
      delete el._copyHandler
    }
  },
}
