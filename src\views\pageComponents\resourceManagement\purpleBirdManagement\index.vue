<template>
  <div class="main-out">
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="1" tab="主账号" v-if="btnPermission[86100]"></a-tab-pane>
      <a-tab-pane key="2" tab="设备" force-render v-if="btnPermission[86200]"></a-tab-pane>
      <a-tab-pane key="3" tab="员工账号" v-if="btnPermission[86300]"></a-tab-pane>
    </a-tabs>
    <mainAccount v-show="activeKey === '1'" v-if="btnPermission[86100]"></mainAccount>
    <device v-show="activeKey === '2'" v-if="btnPermission[86200]"></device>
    <staffAccount v-show="activeKey === '3'" v-if="btnPermission[86300]"></staffAccount>
  </div>
</template>
<script lang="ts" setup>
import mainAccount from './components/mainAccount/index.vue'
import device from './components/device/index.vue'
import staffAccount from './components/staffAccount/index.vue'

const activeKey = ref('1')
const { btnPermission } = usePermission()
</script>
<style lang="scss" scoped>
.main-out {
  display: flex;
  flex-direction: column;
  height: 100%;
}

:deep(.ant-tabs) {
  position: absolute;
  margin-top: -8px;
}
</style>
