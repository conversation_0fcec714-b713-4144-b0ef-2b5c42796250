<template>
  <div class="main">
    <Form v-model:form="formArr" :page-type="PageType.COUNTRY" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.COUNTRY" :get-list="GetList">
      <template #status="{ row }">
        <a-switch :disabled="!btnPermission['83004']" class="btn" @click="tapSwitch($event, row)" v-model:checked="[false, true][row.status]" checked-children="启用" un-checked-children="停用" />
      </template>

      <!-- <template #source_type="{ row }">
        <div>{{ row.source_type == 3 ? 'MDM' : '' }}</div>
      </template> -->

      <template #operate="{ row }">
        <a-button id="roleManagementDetail" @click="detail(row)">查看</a-button>
      </template>
    </BaseTable>

    <!-- 查看 -->
    <detail-drawer ref="detailDrawerRef" />
    <a-modal :zIndex="1000" v-model:open="visibleData.isShow" :title="visibleData.title">
      <div class="modalContent">{{ visibleData.content }}</div>
      <template #footer>
        <a-button id="roleManagementFormComfirm" v-if="visibleData.isConfirmBtn" style="margin-right: 0.8333rem" type="primary" @click="visibleData.okFn" :danger="visibleData.okType === 'danger'">
          {{ visibleData.confirmBtnText }}
        </a-button>
        <a-button id="roleManagementFormCancel" v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
      </template>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import Form from '@/components/Form.vue'
import { PageType } from '@/common/enum'
import { GetList, UpdateStatus } from '@/servers/CountryRegion'
import { onMounted } from 'vue'
import DetailDrawer from './components/DetailDrawer.vue'

const { btnPermission } = usePermission()

// 查看
const detailDrawerRef = ref()
const visibleData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {
    visibleData.isShow = false
  },
})
const formArr: any = ref([
  {
    label: '搜索国家/地区编码',
    value: null,
    type: 'input',
    key: 'country_region_code',
  },
  {
    label: '搜索国家/地区名称',
    value: null,
    type: 'input',
    key: 'country_region_name',
  },
  {
    label: '状态',
    value: null,
    type: 'select',
    selectArr: [
      { label: '启用', value: 1 },
      { label: '停用', value: 0 },
    ],
    key: 'status',
  },
])

const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.COUNTRY) {
    const arr: any[] = []
    obj.COUNTRY.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}
onMounted(() => {
  search()
  initScreening()
})

const tapSwitch = (e, row) => {
  if (!row.status) {
    updateRoleStatus({ id: row.id, status: e ? 1 : 0 })
  } else {
    visibleData.isShow = true
    visibleData.title = '停用国家/地区'
    visibleData.content = `即将停用该国家/地区，停用后：
  · 店铺信息维护时将无法选择当前地区

  确定要停用该国家/地区吗？`
    visibleData.confirmBtnText = '确定'
    visibleData.okType = 'danger'
    visibleData.isCancelBtn = true
    visibleData.okFn = () => {
      updateRoleStatus({ id: row.id, status: e ? 1 : 0 })
    }
  }
}

// 详情
const detail = (item) => {
  detailDrawerRef.value?.open(item.id, btnPermission.value[83003])
}

const tableRef = ref()
const search = () => tableRef.value.search()

// 启用停用
const updateRoleStatus = (obj) => {
  UpdateStatus(obj).then(() => {
    tableRef.value.search()
    visibleData.isShow = false
  })
}
</script>
<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .breadcrumbBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 2.5rem;
    margin: 0.6667rem 0;

    .title {
      font-size: 1.3333rem;
      font-weight: bold;
      color: #000;
    }
  }

  .btnlist {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    margin-top: 1.25rem;
  }

  .formBox {
    .operatingAreaBox {
      display: flex;
      align-items: center;
      height: 2.6667rem;

      .tag {
        padding: 0 0.8333rem;
        cursor: pointer;
        user-select: none;
      }

      .btn {
        margin-right: 0.8333rem;
      }
    }
  }


}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 8.3333rem;
    min-width: 8.3333rem;
    margin-right: 2.5rem;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}

.tableBtn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 2.6667rem;
  margin-top: 0.8333rem;
  margin-bottom: 0.8333rem;
  font-size: 1.3333rem;
  font-weight: bold;
  color: #000;
}

.vxe-icon {
  width: 1.3333rem;
  height: 1.3333rem;
}
</style>
