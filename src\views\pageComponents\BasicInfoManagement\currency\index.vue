<template>
  <div class="main">
    <SearchForm ref="formRef" v-model:form="formArr" :page-type="PageType.Currency" @search="search" @setting="tableRef?.showTableSetting()"></SearchForm>

    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.Currency" :get-list="GetUserList" :isCheckbox="true">
      <template #right-btn>
        <a-button @click="tapSwitch(false, null)" v-if="btnPermission[52002]">批量停用</a-button>
        <a-button @click="tapSwitch(true, null)" v-if="btnPermission[52002]">批量启用</a-button>
      </template>
      <!-- 状态 -->
      <template #status="{ row }">
        <a-switch class="btn" @click="tapSwitch($event, row)" :disabled="!btnPermission[52002]" v-model:checked="[false, true][row.status]" checked-children="启用" un-checked-children="停用" />
      </template>
      <template #operate="{ row }">
        <a-button type="text" @click="openDawer(row)" v-if="btnPermission[52001]">查看</a-button>
      </template>
    </BaseTable>
    <!-- 停用启用 -->
    <a-modal :zIndex="1000" v-model:open="visibleData.isShow" :title="visibleData.title">
      <div class="modalContent">{{ visibleData.content }}</div>
      <template #footer>
        <a-button id="roleManagementFormComfirm" v-if="visibleData.isConfirmBtn" type="primary" @click="visibleData.okFn" :danger="visibleData.okType === 'danger'">
          {{ visibleData.confirmBtnText }}
        </a-button>
        <a-button id="roleManagementFormCancel" v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
      </template>
    </a-modal>
    <infoModel ref="InfoModelRef"></infoModel>
  </div>
</template>
<script lang="ts" setup>
import { PageType } from '@/common/enum'
import { GetCurrencyList, GetCurrencyDetail, GetCurrencyUpdateStatus, GetDropdownItems } from '@/servers/BasicInfoManagement'
import { message } from 'ant-design-vue'
import infoModel from '../components/infoModel.vue'

const InfoModelRef = ref()
const tableRef = ref()
const formRef = ref()
const formArr = ref([
  {
    label: '货币名称',
    value: null,
    type: 'input',
    key: 'currency_name',
  },
  {
    label: '货币代码',
    value: null,
    type: 'input',
    key: 'currency_code',
  },
  {
    label: '状态',
    type: 'select',
    value: null,
    key: 'status',
    options: [],
    width: 200,
  },

  {
    label: '创建日期',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['creation_time_start', 'creation_time_end'],
    placeholder: ['创建开始日期', '结束日期'],
  },
  {
    label: '修改日期',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['modification_time_start', 'modification_time_end'],
    placeholder: ['最后修改日期', '结束日期'],
  },
])
const { btnPermission } = usePermission()
const visibleData = ref({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {},
})
const Infotable = ref([
  {
    title: '基本信息',
    tableItem: [
      // {
      //   width: 100,
      //   labelwidth: 150,
      //   label: '货币编码',
      //   key: 'number',
      //   type: 'text',
      // },
      {
        width: 100,
        labelwidth: 150,
        label: '货币名称',
        key: 'currency_name',
        type: 'text',
      },
      {
        width: 100,
        labelwidth: 150,
        label: '货币代码',
        key: 'currency_code',
        type: 'text',
      },
      // {
      //   width: 100,
      //   labelwidth: 150,
      //   label: '单据显示货币符号',
      //   key: 'number',
      //   type: 'text',
      // },
      {
        width: 100,
        labelwidth: 150,
        label: '货币符号',
        key: 'symbol',
        type: 'text',
      },
      {
        width: 100,
        labelwidth: 150,
        label: '显示格式',
        key: '',
        type: 'xsgstext',
      },
      {
        width: 100,
        labelwidth: 150,
        label: '单价精度',
        key: 'price_digits',
        type: 'text',
      },
      {
        width: 100,
        labelwidth: 150,
        label: '金额精度',
        key: 'amount_digits',
        type: 'text',
      },
      // {
      //   width: 100,
      //   labelwidth: 150,
      //   label: '优先级',
      //   key: 'number',
      //   type: 'text',
      // },
      // {
      //   width: 100,
      //   labelwidth: 150,
      //   label: '中间币',
      //   key: 'number',
      //   type: 'text',
      // },
      {
        width: 100,
        labelwidth: 150,
        label: '舍入类型',
        key: 'round_type_str',
        type: 'text',
      },
      {
        width: 100,
        labelwidth: 150,
        label: '状态',
        key: 'status',
        type: 'textoptions',
        options: [
          {
            label: 565,
            value: 1,
          },
          {
            label: 25,
            value: 2,
          },
          {
            label: 3,
            value: 3,
          },
        ],
      },
    ],
  },
  {
    title: '其他信息',
    tableItem: [
      {
        width: 50,
        labelwidth: 150,
        label: '创建时间',
        key: 'create_at',
        type: 'text',
      },
      {
        width: 50,
        labelwidth: 150,
        label: '创建人',
        key: 'creator',
        key2: 'job_of_creator',
        key3: 'depart_of_creator',
        type: 'namanddepartmenttext',
      },
      {
        width: 50,
        labelwidth: 150,
        label: '最后修改时间',
        key: 'modified_at',
        type: 'text',
      },
      {
        width: 50,
        labelwidth: 150,
        label: '最后修改人',
        key: 'modifier',
        key2: 'job_of_modifier',
        key3: 'depart_of_modifier',
        type: 'namanddepartmenttext',
      },
    ],
  },
])
onMounted(() => {
  GetDropdownItemsdata()
})
const GetDropdownItemsdata = async () => {
  const res = await GetDropdownItems()
  if (res.success == true) {
    // res.data.language_list.forEach((item) => {
    //   item.id = parseInt(item.id)
    // })
    res.data.status_list.forEach((item) => {
      item.value = parseInt(item.value)
    })
    formArr.value.forEach((item) => {
      if (item.key == 'status') {
        item.options = res.data.status_list
      }
    })
    Infotable.value.forEach((item) => {
      item.tableItem.forEach((titem) => {
        if (titem.key == 'status') {
          titem.options = res.data.status_list
        }
      })
    })
  } else {
    // message.warning(res.message)
  }
}
const openDawer = async (item) => {
  const res = await GetCurrencyDetail({ id: item.id })
  if (res.success == true) {
    const data = {
      ...res.data,
      ...res.data.audit_info,
    }
    InfoModelRef.value.init(Infotable.value, data, '查看货币', 'Currency')
  } else {
    // message.warning(res.message)
  }
}
const search = () => tableRef.value.search()
const GetUserList = async (params: any) => {
  params.is_get_total = true
  const res = await GetCurrencyList(params)
  return {
    data: {
      list: res.data.list,
      total: res.data.total,
    },
  }
}
const tapSwitch = (value, titem) => {
  const ids: any = []
  if (titem == null) {
    tableRef.value.checkItemsArr.forEach((item) => {
      ids.push((item as any).id)
    })
  } else {
    ids.push(titem.id)
  }
  if (ids.length == 0) {
    message.warning('请选择操作数据')
    return
  }
  if (value) {
    // 启用
    visibleData.value.isShow = true
    visibleData.value.title = '启用货币'
    visibleData.value.content = `即将启用该货币，启用后：
    - 新的内容将可以使用该货币。
    - 现有内容中已保留的该货币相关数据也将继续可用。
  确定要启用该货币吗？`
    visibleData.value.confirmBtnText = '启用'
    visibleData.value.isCancelBtn = true
    visibleData.value.okType = 'primary'
    visibleData.value.okFn = async () => {
      const params = {
        ids,
        status: '启用',
      }
      const res = await GetCurrencyUpdateStatus(params)
      if (res.success == true) {
        search()
        message.success('操作成功')
      } else {
        // message.warning(res.message)
      }
      visibleData.value.isShow = false
    }
  }
  if (!value) {
    // 停用
    visibleData.value.isShow = true
    visibleData.value.title = '停用货币'
    visibleData.value.content = `即将停用该货币，停用后：
    - 新的内容将无法使用该货币。
    - 现有内容中仍会保留该货币的相关数据。
  确定要停用该货币吗？`
    visibleData.value.confirmBtnText = '停用'
    visibleData.value.isCancelBtn = true
    visibleData.value.okType = 'primary'
    visibleData.value.okFn = async () => {
      const params = {
        ids,
        status: '停用',
      }
      const res = await GetCurrencyUpdateStatus(params)
      if (res.success == true) {
        search()
        message.success('操作成功')
      } else {
        // message.warning(res.message)
      }
      visibleData.value.isShow = false
    }
  }
}
</script>
<style lang="scss">
.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}
</style>
