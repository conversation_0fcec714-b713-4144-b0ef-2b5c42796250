// 紫鸟管理模块
import { request } from './request'

// 获取紫鸟主账号列表
export const GetMainAccountList = (data) => {
  return request({ url: '/api/PurpleBird/PrimaryAccount/List', data })
}
// 获取紫鸟主账号详情
export const GetMainAccountDetail = (data) => {
  return request({ url: '/api/PurpleBird/PrimaryAccount/Details', data })
}
// 获取紫鸟主账号日志
export const GetMainAccountLog = (data) => {
  return request({ url: '/api/PurpleBird/PrimaryAccount/OpLogInfos', data })
}
// 获取紫鸟主账号密码
export const GetMainAccountPassword = (data) => {
  return request({ url: '/api/PurpleBird/PrimaryAccount/Cipher', data })
}
// 创建紫鸟主账号
export const AddMainAccount = (data) => {
  return request({ url: '/api/PurpleBird/PrimaryAccount/Add', data })
}
// 编辑紫鸟主账号
export const UpdateMainAccount = (data) => {
  return request({ url: '/api/PurpleBird/PrimaryAccount/Update', data })
}
// 删除紫鸟主账号
export const DeleteMainAccount = (data) => {
  return request({ url: '/api/PurpleBird/PrimaryAccount/Delete', data })
}

/* 获取紫鸟企业列表 */
export const GetPurpleBirdCompanyList = () => {
  return request({ url: '/api/PurpleBird/PrimaryAccount/PrimaryAccountScreenList' })
}

// 获取紫鸟设备列表
export const GetDeviceList = (data) => {
  return request({ url: '/api/PurpleBird/Device/List', data })
}
// 创建紫鸟设备
export const AddDevice = (data) => {
  return request({ url: '/api/PurpleBird/Device/Add', data })
}
// 编辑紫鸟设备
export const UpdateDevice = (data) => {
  return request({ url: '/api/PurpleBird/Device/Update', data })
}
// 删除紫鸟设备
export const DeleteDevice = (data) => {
  return request({ url: '/api/PurpleBird/Device/Delete', data })
}
// 获取紫鸟设备详情
export const GetDeviceDetail = (data) => {
  return request({ url: '/api/PurpleBird/Device/Details', data })
}
// 获取紫鸟设备日志
export const GetDeviceLog = (data) => {
  return request({ url: '/api/PurpleBird/Device/OpLogInfos', data })
}

/* 查看紫鸟设备关联店铺 */
export const GetBindStore = (data) => {
  return request({ url: '/api/PurpleBird/Device/AssociationShop', data })
}

// 获取紫鸟员工账号列表
export const GetStaffList = (data) => {
  return request({ url: '/api/PurpleBird/Staff/List', data })
}

// 获取紫鸟员工账号详情
export const GetStaffDetail = (data) => {
  return request({ url: '/api/PurpleBird/Staff/Details', data })
}

// 获取紫鸟子账号日志
export const GetStaffLog = (data) => {
  return request({ url: '/api/PurpleBird/Staff/OpLogInfos', data })
}

// 获取员工账号密码
export const GetStaffPassword = (data) => {
  return request({ url: '/api/PurpleBird/Staff/Cipher', data })
}

// 创建紫鸟员工账号
export const AddStaff = (data) => {
  return request({ url: '/api/PurpleBird/Staff/Add', data })
}
// 编辑紫鸟员工账号
export const UpdateStaff = (data) => {
  return request({ url: '/api/PurpleBird/Staff/Update', data })
}
// 删除紫鸟员工账号
export const DeleteStaff = (data) => {
  return request({ url: '/api/PurpleBird/Staff/Delete', data })
}

// 获取紫鸟企业下拉列表(免鉴权)
export const GetPurpleBirdAccountOptions = () => {
  return request({ url: '/api/PurpleBird/PrimaryAccount/PrimaryAccountScreenList' })
}
// 获取紫鸟设备下拉列表(免鉴权)
export const GetPurpleBirdDeviceOptions = (data) => {
  return request({ url: '/api/PurpleBird/Device/DeviceScreenList', data })
}
