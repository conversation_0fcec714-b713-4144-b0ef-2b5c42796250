<template>
  <div class="main">
    <Form v-model:form="formArr" :page-type="PageType.LegalPerson_GetList" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.LegalPerson_GetList" :get-list="GetList">
      <template #status="{ row }">
        <a-switch :disabled="!btnPermission['89003']" class="btn" @click="tapSwitch($event, row)" v-model:checked="[false, true][row.status]" checked-children="启用" un-checked-children="停用" />
      </template>

      <template #attachment="{ row }">
        <a-space>
          <span v-if="!row.files" @click="onLook(row)" :class="['look-btn', btnPermission[89005] ? 'blue' : 'forbid']">点击查看</span>
          <span v-else-if="!row.files.length">--</span>
          <span v-else class="files_link" v-for="(item, index) in row.files" :key="item" @click="clickFiles(item)">
            {{ row.document_type_name + (index + 1) }}
          </span>
        </a-space>
      </template>

      <template #operate="{ row }">
        <a-button id="roleManagementDetail" :disabled="!btnPermission['89002']" @click="detail(row)">查看</a-button>
      </template>
    </BaseTable>

    <!-- 查看 -->
    <detail-drawer ref="detailDrawerRef" />
    <a-modal :zIndex="1000" v-model:open="visibleData.isShow" :title="visibleData.title">
      <div class="modalContent">{{ visibleData.content }}</div>
      <template #footer>
        <a-button id="roleManagementFormComfirm" v-if="visibleData.isConfirmBtn" style="margin-right: 0.8333rem" type="primary" @click="visibleData.okFn" :danger="visibleData.okType === 'danger'">
          {{ visibleData.confirmBtnText }}
        </a-button>
        <a-button id="roleManagementFormCancel" v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
      </template>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import Form from '@/components/Form.vue'
import { PageType } from '@/common/enum'
import { GetEnum } from '@/servers/Common'
import { GetList, UpdateStatus, GetAttachments } from '@/servers/LegalPersonManagement'
import { onMounted } from 'vue'
import DetailDrawer from './components/DetailDrawer.vue'

const { btnPermission } = usePermission()

// 查看
const detailDrawerRef = ref()
const visibleData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {
    visibleData.isShow = false
  },
})
const formArr: any = ref([
  {
    label: '搜索法人名称',
    value: null,
    type: 'input',
    key: 'name',
  },
  {
    label: '搜索证件号码',
    value: null,
    type: 'input',
    key: 'document_number',
  },
  {
    label: '搜索手机号码',
    value: null,
    type: 'input',
    key: 'phone_number',
  },
  {
    label: '证件类型',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'document_type',
  },
  {
    label: '状态',
    value: null,
    type: 'select',
    selectArr: [
      { label: '启用', value: 1 },
      { label: '停用', value: 0 },
    ],
    key: 'status',
  },
])

const onLook = (item) => {
  if (!btnPermission.value[89005]) return
  GetAttachments({ id: item.id }).then((res) => {
    item.files = res.data || []
  })
}

// 查看附件
const clickFiles = (item) => {
  window.open(item, '_blank')
}

const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.LegalPerson_GetList) {
    const arr: any[] = []
    obj.LegalPerson_GetList.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}
onMounted(() => {
  search()
  GetEnum().then((res) => {
    formArr.value.find((n) => n.key === 'document_type').selectArr = res.data.data.document_type
  })
  initScreening()
})

const tapSwitch = (e, row) => {
  if (!row.status) {
    UpdateStatuss({ id: row.id, status: e ? 1 : 0 })
  } else {
    visibleData.isShow = true
    visibleData.title = '停用法人'
    visibleData.content = `即将停用该法人，停用后：
        · 店铺信息维护时将无法选择当前法人
        · 申请开店时无法使用该法人
       确定要停用该法人吗？`
    visibleData.confirmBtnText = '确定'
    visibleData.okType = 'danger'
    visibleData.isCancelBtn = true
    visibleData.okFn = () => {
      UpdateStatuss({ id: row.id, status: e ? 1 : 0 })
    }
  }
}

// 详情
const detail = (item) => {
  detailDrawerRef.value?.open(item.id, btnPermission.value[89004])
}

const tableRef = ref()
const search = () => tableRef.value.search()

// 启用停用
const UpdateStatuss = (obj) => {
  UpdateStatus(obj).then(() => {
    tableRef.value.search()
    visibleData.isShow = false
  })
}
</script>
<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .breadcrumbBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 2.5rem;
    margin: 0.6667rem 0;

    .title {
      font-size: 1.3333rem;
      font-weight: bold;
      color: #000;
    }
  }

  .btnlist {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    margin-top: 1.25rem;
  }

  .formBox {
    .operatingAreaBox {
      display: flex;
      align-items: center;
      height: 2.6667rem;

      .tag {
        padding: 0 0.8333rem;
        cursor: pointer;
        user-select: none;
      }

      .btn {
        margin-right: 0.8333rem;
      }
    }
  }


}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 8.3333rem;
    min-width: 8.3333rem;
    margin-right: 2.5rem;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}

.tableBtn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 2.6667rem;
  margin-top: 0.8333rem;
  margin-bottom: 0.8333rem;
  font-size: 1.3333rem;
  font-weight: bold;
  color: #000;
}

.vxe-icon {
  width: 1.3333rem;
  height: 1.3333rem;
}

.files_link {
  color: #015bea;
  cursor: pointer;
}

.look-btn {
  cursor: pointer;
}

.blue {
  color: #1890ff;
}

.grey {
  color: #aaa;
}

.forbid {
  cursor: not-allowed;
}
</style>
