<template>
  <a-select
    :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
    v-model:value="displayValue"
    :placeholder="item.label"
    :filter-option="filterOption"
    :mode="(item as any).multiple ? 'multiple' : null"
    :maxTagCount="(item as any).multiple ? 'responsive' : null"
    labelInValue
    allowClear
    showArrow
    class="w-140px"
    @change="handleChange"
  >
    <!-- 使用插槽方式渲染选项，确保正确显示 -->
    <a-select-option v-for="option in (item as any).options" :key="option.value" :value="option.value" :label="option.label">
      {{ option.label }}
    </a-select-option>
  </a-select>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { filterOption } from '@/utils/index'
import { FormItemType } from '../type'

defineEmits<{
  (e: 'serach'): void
}>()

const item = defineModel<FormItemType<'select'>>('item', { required: true })

// 计算显示值：将原始值转换为 labelInValue 格式
const displayValue = computed({
  get() {
    const options = (item.value as any).options || []
    const rawValue = item.value.value

    console.log('🔍 [displayValue.get] 计算显示值:', {
      key: (item.value as any).key,
      rawValue,
      optionsLength: options.length,
      multiple: (item.value as any).multiple,
    })

    if (!rawValue || (Array.isArray(rawValue) && rawValue.length === 0)) {
      return (item.value as any).multiple ? [] : undefined
    }

    if ((item.value as any).multiple && Array.isArray(rawValue)) {
      // 多选模式：返回 {label, value} 对象数组
      const result = rawValue.map((val: any) => {
        const option = options.find((opt: any) => opt.value === val || opt.value === String(val))
        const displayItem = option ? { label: option.label, value: option.value } : { label: String(val), value: val }
        console.log('🔍 [displayValue.get] 多选项:', { val, option, displayItem })
        return displayItem
      })
      console.log('🔍 [displayValue.get] 多选结果:', result)
      return result
    }
    // 单选模式：返回 {label, value} 对象
    const option = options.find((opt: any) => opt.value === rawValue || opt.value === String(rawValue))
    const result = option ? { label: option.label, value: option.value } : { label: String(rawValue), value: rawValue }
    console.log('🔍 [displayValue.get] 单选结果:', result)
    return result
  },
  set(newValue: any) {
    console.log('🔍 [displayValue.set] 设置新值:', {
      key: (item.value as any).key,
      newValue,
      multiple: (item.value as any).multiple,
    })

    // 将 labelInValue 格式转换回原始值
    if (!newValue || (Array.isArray(newValue) && newValue.length === 0)) {
      item.value.value = (item.value as any).multiple ? [] : null
      return
    }

    if ((item.value as any).multiple && Array.isArray(newValue)) {
      // 多选模式：提取 value 数组
      item.value.value = newValue.map((item: any) => item.value)
    } else {
      // 单选模式：提取 value
      item.value.value = newValue.value
    }

    console.log('🔍 [displayValue.set] 设置后的原始值:', item.value.value)
  },
})

const handleChange = (value: any) => {
  console.log('🔍 [FormSelect] handleChange 被调用:', {
    key: (item.value as any).key,
    oldValue: item.value.value,
    newValue: value,
    optionsLength: (item.value as any).options?.length,
    multiple: (item.value as any).multiple,
    rawValues: Array.isArray(value) ? value.map((v: any) => v.value) : value?.value,
  })

  // 调用配置中的onChange回调
  if ((item.value as any).onChange && typeof (item.value as any).onChange === 'function') {
    console.log('🔍 [FormSelect] 调用配置中的onChange回调')
    const rawValue = Array.isArray(value) ? value.map((v: any) => v.value) : value?.value
    ;(item.value as any).onChange(rawValue)
  }
}
</script>

<style scoped></style>
