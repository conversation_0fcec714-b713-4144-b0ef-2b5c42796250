<template>
  <div class="quickBox" v-if="isQuicks">
    <a-space>
      <template v-for="item in quicks" :key="item.key">
        <div class="quickLine">
          <div class="quickLabel">{{ item.quickLabel || item.label }}：</div>
          <div class="quickContent">
            <div
              class="quickItem"
              :class="{
                active: !item.value || item?.value.length === 0,
              }"
              @click="handleQuick(item, null)"
              v-if="!item.quickNotFull"
            >
              全部
              <div :class="['checkIcon', { '!opacity-100': !item.value || item?.value.length === 0 }]"><CheckOutlined class="icon" /></div>
            </div>

            <template v-for="v in item.quicks ? item.quicks : item.selectArr || item.options" :key="v.value">
              <div
                class="quickItem"
                :class="{
                  active: Array.isArray(item.value) ? item.value == v.value || item.value.includes(v?.value || v) : item.value == (v?.value || v),
                  // multiple: item.multiple
                }"
                @click="handleQuick(item, v)"
              >
                {{ v.label || v }}

                <span v-if="v.count" :style="item.isCountRemind ? 'color: red' : ''">{{ item.isCountRemind ? '+' : '' }}{{ v.count }}</span>
                <div :class="['checkIcon', { '!opacity-100': Array.isArray(item.value) ? item.value == v.value || item.value.includes(v?.value || v) : item.value == (v?.value || v) }]">
                  <CheckOutlined class="icon" />
                </div>
              </div>
            </template>
          </div>
        </div>
        <div v-if="item.line" class="partingLine"></div>
      </template>
    </a-space>
  </div>
</template>

<script setup lang="ts">
import { CheckOutlined } from '@ant-design/icons-vue'

defineProps<{
  isQuicks?: boolean
}>()

const emits = defineEmits<{
  (e: 'search'): void
}>()

const quicks = defineModel<any[]>('quicks', { default: () => [] })

const handleQuick = (item, v) => {
  const value = v?.value || v

  if (v === null) item.value = item.multiple ? [] : null
  else {
    if (item.multiple) {
      if (item.value && item.value.includes(value)) {
        item.value = item.value.filter((x) => x != value)
      } else {
        item.value = item.value ? item.value.concat(value) : [value]
      }
    } else item.value = value
  }

  if (item.onChange) item.onChange()

  nextTick(() => emits('search'))
}
</script>

<style scoped lang="scss">
.quickBox {
  display: flex;
  flex-wrap: wrap;
  color: #666;

  .quickLine {
    display: flex;

    .quickLabel {
      box-sizing: border-box;
      padding-top: 3px;
      font-weight: 700;
    }

    .quickContent {
      display: flex;
      flex: 1;
      flex-wrap: wrap;

      .quickItem {
        position: relative;
        height: 24px;
        padding: 3px 8px;
        margin-right: 4px;
        margin-bottom: 8px;
        overflow: hidden;
        font-size: 12px;
        color: #666;
        cursor: pointer;
        background-color: #ebebeb;
        border: 1px solid #ebebeb;
        border-radius: 4px;
        transition: 0.3s all;

        &:hover {
          opacity: 1;

          &.active {
            opacity: 0.7;
          }
        }

        &.active {
          color: #448ef7;
          background: #fff;
          border: 1px solid #448ef7;
          opacity: 1;
        }
      }

      .checkIcon {
        position: absolute;
        top: -2px;
        right: -7px;
        width: 0;
        height: 0;
        border-right: 10px solid transparent;
        border-bottom: 10px solid #1890ff;
        border-left: 10px solid transparent;
        opacity: 0;
        transition: all 0.3s;
        transform: rotate(45deg);

        .icon {
          position: relative;
          top: -5px;
          left: -3px;
          font-size: 6px;
          color: #fff;
          transform: rotate(-45deg);
        }
      }
    }
  }

  .partingLine {
    width: 1px;
    height: 15px;
    margin-inline: 12px;
    margin-top: 5px;
    background-color: #dcdcdc;
  }
}
</style>
