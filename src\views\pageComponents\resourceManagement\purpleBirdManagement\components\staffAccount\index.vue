<template>
  <div class="main">
    <Form v-model:form="formArr" :page-type="PageType.PURPLE_BIRD_STAFF_ACCOUNT" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.PURPLE_BIRD_STAFF_ACCOUNT" :get-list="GetStaffList">
      <template #left-btn>
        <a-button id="roleManagementIncrease" type="primary" @click="onOpenEdit(null, 'add')" v-if="btnPermission[86301]">创建员工账号</a-button>
      </template>
      <template #password="{ row }">
        <div v-if="!row?.password" :class="['lookBtn', btnPermission[86306] ? 'blue' : 'grey forbid']" @click="onLookPwd(row)">点击查看</div>
        <div v-else>
          <div class="lookBox">
            <div class="lookText">{{ row.password }}</div>
          </div>
        </div>
      </template>
      <template #operate="{ row }">
        <a-button :disabled="!btnPermission[86302]" @click="detail(row)" class="mr-10px">查看</a-button>
        <a-button :disabled="!btnPermission[86303]" @click="onOpenEdit(row.id, 'edit')" class="mr-10px">编辑</a-button>
        <a-button :disabled="!btnPermission[86304]" @click="onDelete(row)">删除</a-button>
      </template>
    </BaseTable>

    <!-- 查看 -->
    <DetailDrawer ref="detailDrawerRef" />
    <EditDrawer ref="editDrawerRef" @success="search" />
    <a-modal :zIndex="1000" v-model:open="visibleData.isShow" :title="visibleData.title">
      <div class="modalContent">{{ visibleData.content }}</div>
      <template #footer>
        <a-button id="roleManagementFormComfirm" v-if="visibleData.isConfirmBtn" style="margin-right: 0.8333rem" type="primary" @click="visibleData.okFn" :danger="visibleData.okType === 'danger'">
          {{ visibleData.confirmBtnText }}
        </a-button>
        <a-button id="roleManagementFormCancel" v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
      </template>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import Form from '@/components/Form.vue'
import { PageType } from '@/common/enum'
import { GetStaffList, GetStaffPassword, DeleteStaff, GetPurpleBirdCompanyList } from '@/servers/PurpleBird'
import { onMounted } from 'vue'
import message from 'ant-design-vue/es/message'
import DetailDrawer from './components/DetailDrawer.vue'
import EditDrawer from './components/EditDrawer.vue'

const { btnPermission } = usePermission()
const editDrawerRef = ref()

// 查看
const detailDrawerRef = ref()
const visibleData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {
    visibleData.isShow = false
  },
})
const formArr: any = ref([
  {
    label: '搜索紫鸟员工账号',
    value: null,
    type: 'input',
    key: 'account_number',
  },
  {
    label: '紫鸟企业',
    value: null,
    type: 'select',
    key: 'purple_bird_primary_account_id',
    selectArr: [],
  },
])

// 查看密码
const onLookPwd = (row) => {
  if (!btnPermission.value[86306]) {
    return
  }
  GetStaffPassword({ id: row.id }).then((res) => {
    row.password = res.data.password
  })
}

const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.PURPLE_BIRD_STAFF_ACCOUNT) {
    const arr: any[] = []
    obj.PURPLE_BIRD_STAFF_ACCOUNT.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}
onMounted(() => {
  search()
  getOptions()
  initScreening()
})

// 详情
const detail = (item) => {
  detailDrawerRef.value?.open(item.id, btnPermission.value[86305])
}

const onOpenEdit = (id, type) => {
  editDrawerRef.value?.open(id, type)
}

const onDelete = (item) => {
  visibleData.isShow = true
  visibleData.title = '删除紫员工账号'
  visibleData.content = `即将删除该账号，删除后：
      · 用户无法查看紫鸟账号信息，紫鸟仍可正常使用。
  请在执行此操作前确认：
      · 已检查并确认紫鸟中同步删除当前账号。
  此操作不可恢复，确定要删除该账号吗？`
  visibleData.okFn = async () => {
    await DeleteStaff({ id: item.id })
    message.success('删除成功')
    tableRef.value.refresh()
    visibleData.isShow = false
  }
}

const getOptions = async () => {
  const componyOptions = (await GetPurpleBirdCompanyList()).data
  formArr.value.find((x) => x.key === 'purple_bird_primary_account_id').selectArr = componyOptions.map((x) => ({ label: x.name, value: x.id }))
}

const tableRef = ref()
const search = () => tableRef.value.search()
</script>
<style lang="scss" scoped>
.main {
  padding-top: 0;
  padding-right: 0;
  padding-left: 0;

  .breadcrumbBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 2.5rem;
    margin: 0.6667rem 0;

    .title {
      font-size: 1.3333rem;
      font-weight: bold;
      color: #000;
    }
  }

  .btnlist {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    margin-top: 1.25rem;
  }

  .formBox {
    .operatingAreaBox {
      display: flex;
      align-items: center;
      height: 2.6667rem;

      .tag {
        padding: 0 0.8333rem;
        cursor: pointer;
        user-select: none;
      }

      .btn {
        margin-right: 0.8333rem;
      }
    }
  }


}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 8.3333rem;
    min-width: 8.3333rem;
    margin-right: 2.5rem;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}

.tableBtn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 2.6667rem;
  margin-top: 0.8333rem;
  margin-bottom: 0.8333rem;
  font-size: 1.3333rem;
  font-weight: bold;
  color: #000;
}

.vxe-icon {
  width: 1.3333rem;
  height: 1.3333rem;
}

.lookBtn {
  cursor: pointer;
}

.blue {
  color: #1890ff;
}

.grey {
  color: #aaa;
}

.forbid {
  cursor: not-allowed;
}
</style>
