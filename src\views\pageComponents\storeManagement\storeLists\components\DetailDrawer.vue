<template>
  <a-drawer
    :footer="logVisble ? undefined : false"
    v-model:open="detailVisible"
    :width="appStore.isOpenLog ? '91vw' : '45vw'"
    title="查看店铺"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
    :bodyStyle="{ padding: '0' }"
  >
    <template #extra v-if="btnPermission[71001]">
      <a-button @click="changeLogVisible">日志</a-button>
    </template>
    <div class="detailAllBox">
      <a-form>
        <a-collapse v-model:activeKey="activeKey" :bordered="true" expandIconPosition="end" ghost>
          <template #expandIcon="{ isActive }">
            <DoubleRightOutlined :rotate="isActive ? 270 : 90" />
          </template>

          <a-collapse-panel key="1" header="基础信息" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="8">
                <p class="label">店铺名称</p>
                <p class="value">{{ formData.shop_name }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">平台（大类）</p>
                <p class="value">{{ formData.shop_platform }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">平台（小类）</p>
                <p class="value">{{ formData.shop_platform_subtype }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">店铺类型</p>
                <p class="value">{{ formData.shop_type_name }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">
                  国家/地区
                  <a-tooltip title="指店铺注册关联的主体归属的国家/地区">
                    <InfoCircleFilled style="font-size: 14px; color: #333" />
                  </a-tooltip>
                </p>
                <p class="value">{{ formData.shop_country }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">
                  站点
                  <a-tooltip title="指店铺实际运营的国家/地区">
                    <InfoCircleFilled style="font-size: 14px; color: #333" />
                  </a-tooltip>
                </p>
                <p class="value">{{ formData.shop_site }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">店铺状态</p>
                <p class="value">{{ formData.shop_status_name }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">邮箱</p>
                <p class="value">{{ formData.shop_email }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">手机号</p>
                <p class="value">{{ formData.shop_phone }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">店铺保证金</p>
                <p class="value">{{ formData.shop_deposit_amount }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">营业执照</p>
                <p class="value">{{ formData.business_license }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">法人姓名</p>
                <p class="value">{{ formData.legal_rep_name }}</p>
              </a-col>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel key="2" header="归属信息" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="8">
                <p class="label">公司</p>
                <p class="value">{{ formData.sub_company_name }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">部门</p>
                <p class="value">{{ formData.department }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">负责人</p>
                <p class="value">{{ formData.username }}/{{ formData.name }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">创建人</p>
                <p class="value">{{ formData.applicant_username }}/{{ formData.applicant_name }}</p>
              </a-col>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel key="3" header="财务信息" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="8">
                <p class="label">收款资金平台</p>
                <p class="value">{{ formData.payee_account_type_string }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">收款账号</p>
                <p class="value">{{ formData.payee_account }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">收款人名称</p>
                <p class="value">{{ formData.payee_account }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">付款资金平台</p>
                <p class="value">{{ formData.payer_account_type_string }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">付款账号</p>
                <p class="value">{{ formData.payee_name }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">付款人名称</p>
                <p class="value">{{ formData.payer_name }}</p>
              </a-col>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel key="4" header="账号信息" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="8" v-if="btnPermission[71002]">
                <p class="label">总部财务账号</p>
                <div class="value" style="cursor: pointer">
                  <div :class="['lookBtn', btnPermission[71002] ? 'blue' : 'grey']" @click="lookBtn" v-if="!formData.accountNumber">点击查看</div>
                  <div class="userText" v-else>账号：{{ formData.accountNumber?.account }} 密码 {{ formData.accountNumber?.password }}</div>
                </div>
              </a-col>
              <a-col class="gutter-row" :span="8" v-if="btnPermission[71003]">
                <p class="label">分部财务账号</p>
                <div class="value" style="cursor: pointer">
                  <div class="userInput">
                    <div :class="['lookBtn', btnPermission[71003] ? 'blue' : 'grey']" @click="lookBtns" v-if="!formData.accountNumbers">点击查看</div>
                    <div class="userText" v-else>账号：{{ formData.accountNumbers?.account }} 密码 {{ formData.accountNumbers?.password }}</div>
                  </div>
                </div>
              </a-col>
              <a-col class="gutter-row" :span="8" v-if="btnPermission[71004]">
                <p class="label">运营账号</p>
                <div class="value" style="cursor: pointer">
                  <div class="userInput">
                    <div :class="['lookBtn', btnPermission[71004] ? 'blue' : 'grey']" @click="lookOperation" v-if="!formData.ops_account">点击查看</div>
                    <div class="userText" v-else>账号：{{ formData.ops_account?.account }} 密码 {{ formData.ops_account?.password }}</div>
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel key="5" header="外部店铺信息" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="8">
                <p class="label">聚水潭店铺</p>
                <p class="value">
                  {{
                    formData?.jst_shop_list
                      ?.map((n) => n.shop_name)
                      .toString()
                      ?.replaceAll(',', '，') || '无'
                  }}
                </p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">马帮店铺</p>
                <p class="value">
                  {{
                    formData?.mabang_shop_list
                      ?.map((n) => n.shop_name)
                      .toString()
                      ?.replaceAll(',', '，') || '无'
                  }}
                </p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">自研ERP</p>
                <p class="value">
                  {{
                    formData?.erp_shop_list
                      ?.map((n) => n.shop_name)
                      .toString()
                      ?.replaceAll(',', '，') || '无'
                  }}
                </p>
              </a-col>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel key="6" header="紫鸟设备绑定" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="8">
                <p class="label">企业</p>
                <p class="value">{{ formData?.purple_bird_company || '无' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">设备名称</p>
                <p class="value">{{ formData?.purple_bird_device || '无' }}</p>
              </a-col>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel key="7" header="其他" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="8">
                <p class="label">创建时间</p>
                <p class="value">{{ formData.create_time }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">创建人</p>
                <p class="value">
                  <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
                    <template #title>
                      <div>用户名称：{{ formData.applicant_name }}</div>
                      <div>所属公司：{{ formData.applicant_sub_company_name ? formData.applicant_sub_company_name : '--' }}</div>
                      <div>所在部门：{{ formData.applicant_department ? formData.applicant_department : '--' }}</div>
                      <div>
                        岗
                        <span style="visibility: hidden">占位</span>
                        位：{{ formData.applicant_job_name ? formData.applicant_job_name : '--' }}
                      </div>
                    </template>
                    <div style="display: flex; align-items: center">
                      <span class="detailValue">{{ formData.applicant_name ? formData.applicant_name : '--' }}</span>
                      <span v-if="formData.applicant_department || formData.applicant_job_name" class="detailValueDescription">
                        （
                        <span v-if="formData.applicant_job_name">{{ formData.applicant_job_name }}&nbsp;|&nbsp;</span>
                        <span v-if="formData.applicant_department">
                          {{ formData.applicant_department.length > 10 ? formData.applicant_department.slice(0, 10) + '...' : formData.applicant_department }}
                        </span>
                        ）
                      </span>
                    </div>
                  </a-tooltip>
                </p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">最后修改时间</p>
                <p class="value">{{ formData.update_time }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">最后修改人</p>
                <p class="value">
                  <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
                    <template #title>
                      <div>用户名称：{{ formData.updated_name }}</div>
                      <div>所属公司：{{ formData.updated_sub_company_name ? formData.updated_sub_company_name : '--' }}</div>
                      <div>所在部门：{{ formData.updated_department ? formData.updated_department : '--' }}</div>
                      <div>
                        岗
                        <span style="visibility: hidden">占位</span>
                        位：{{ formData.updated_job_name ? formData.updated_job_name : '--' }}
                      </div>
                    </template>
                    <div style="display: flex; align-items: center">
                      <span class="detailValue">{{ formData.updated_name ? formData.updated_name : '--' }}</span>
                      <span v-if="formData.updated_department || formData.updated_job_name" class="detailValueDescription">
                        （
                        <span v-if="formData.updated_job_name">{{ formData.updated_job_name }}&nbsp;|&nbsp;</span>
                        <span v-if="formData.updated_department">{{ formData.updated_department.length > 10 ? formData.updated_department.slice(0, 10) + '...' : formData.updated_department }}</span>
                        ）
                      </span>
                    </div>
                  </a-tooltip>
                </p>
              </a-col>
            </a-row>
          </a-collapse-panel>
        </a-collapse>
      </a-form>
      <LogDrawer ref="LogDrawerRef" class="log" v-if="!detailloading && appStore.isOpenLog" />
    </div>
  </a-drawer>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { HqFinanceAccount, SubFinanceAccount, Details, OpsAccount } from '@/servers/StoreLists'
import { DoubleRightOutlined, InfoCircleFilled } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import useAppStore from '@/store/modules/app'
import LogDrawer from './LogDrawer.vue'

const { btnPermission } = usePermission()
const activeKey = ref(['1', '2', '3', '4', '5', '6', '7'])
const customStyle = 'overflow: hidden; border: .0625rem solid #eee; margin-bottom: 1.25rem;'

const appStore = useAppStore()
const logVisble = ref(false)
const detailloading = ref(false)

const LogDrawerRef = ref()
const detailVisible = ref(false)
const formData: any = ref({})
const lookBtn = () => {
  if (!btnPermission.value[71002]) {
    message.error('暂无查看权限，如需查看请联系管理员。')
    return
  }
  HqFinanceAccount({ shop_id: formData.value.id }).then((res: any) => {
    console.log(res)
    formData.value.accountNumber = res.data
  })
}
const lookBtns = () => {
  if (!btnPermission.value[71003]) {
    message.error('暂无查看权限，如需查看请联系管理员。')
    return
  }
  SubFinanceAccount({ shop_id: formData.value.id }).then((res: any) => {
    console.log(res)
    formData.value.accountNumbers = res.data
  })
}
const lookOperation = () => {
  if (!btnPermission.value[71004]) {
    message.error('暂无查看权限，如需查看请联系管理员。')
    return
  }
  OpsAccount({ shop_id: formData.value.id }).then((res: any) => {
    formData.value.ops_account = res.data
  })
}

const open = (row) => {
  detailVisible.value = true
  Details({ shop_id: row.id }).then((res: any) => {
    console.log(res)
    formData.value = res.data
    nextTick(() => {
      LogDrawerRef.value?.open(formData.value)
    })
  })
}
// 查看日志
const changeLogVisible = () => {
  appStore.changeLogOpenVisible(appStore.isOpenLog ? 0 : 1)
  if (appStore.isOpenLog) {
    nextTick(() => {
      LogDrawerRef.value?.open(formData.value)
    })
  }
}
// 暴露方法
defineExpose({
  open,
})
</script>
<style scoped>
:deep(.ant-collapse-header) {
  background-color: #f4f7fe;
}

.detailBox {
  .conBox {
    display: flex;
    flex-wrap: wrap;
  }

  .formItem {
    display: flex;
    align-items: center;
    width: 420px;
    height: 40px;
    margin: 0 0 10px;
    margin-right: 10px;
    font-size: 14px;
    color: black;

    .itemtitle {
      width: 100px;
      font-size: 12px;
      color: #666;
      text-align: end;
    }

    .itemInput {
      flex: 1;
      padding-left: 20px;
      font-size: 12px;
    }
  }

  .userBox {
    display: flex;
    height: 40px;
    padding-left: 10px;
    margin: 10px 0;
    font-size: 14px;
    line-height: 40px;
    color: black;

    .userTitle {
      width: 110px;
      padding-right: 20px;
      font-size: 12px;
      color: #666;
      text-align: end;
    }

    .lookBtn {
      font-size: 12px;
      cursor: pointer;
    }

    .userText {
      font-size: 12px;
    }
  }
}

.blue {
  color: #1890ff;
}

.grey {
  color: #aaa;
}
</style>
