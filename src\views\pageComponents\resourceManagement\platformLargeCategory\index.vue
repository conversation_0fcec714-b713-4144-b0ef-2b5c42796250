<template>
  <div class="main">
    <Form v-model:form="formArr" :page-type="PageType.PLATFORM_LARGE_CATEGORIES" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.PLATFORM_LARGE_CATEGORIES" :get-list="GetList">
      <template #left-btn>
        <a-button :disabled="!btnPermission[81002]" type="primary" @click="tapManipulate('add')">新建大类</a-button>
      </template>
      <template #brand_authorization="{ row }">
        <span>{{ row.brand_authorization || '--' }}</span>
      </template>
      <template #status="{ row }">
        <a-switch :disabled="!btnPermission['81007']" class="btn" @click="tapSwitch($event, row)" v-model:checked="[false, true][row.status]" checked-children="启用" un-checked-children="停用" />
      </template>
      <template #create_at="{ row }">
        <span>{{ row.create_at ? row.create_at.slice(0, 16) : '' }}</span>
      </template>
      <template #update_at="{ row }">
        <span>{{ row.update_at ? row.update_at.slice(0, 16) : '' }}</span>
      </template>
      <template #operate="{ row }">
        <a-button :disabled="!btnPermission[81003]" @click="detail(row)" class="mr-10px">查看</a-button>
        <a-button :disabled="!btnPermission[81004]" class="mr-10px" @click="tapManipulate('compiler', row)">编辑</a-button>
        <a-button :disabled="!btnPermission[81005]" @click="tapManipulate('removes', row)">删除</a-button>
      </template>
    </BaseTable>

    <a-drawer
      v-model:open="isAddRole"
      @afterOpenChange="formRef.clearValidate()"
      width="520"
      :title="roleModuleType == 'add' ? '新建平台大类' : '编辑平台大类'"
      placement="right"
      :maskClosable="false"
      :footer-style="{ textAlign: 'left' }"
    >
      <a-form ref="formRef" :model="editForm">
        <a-form-item
          label="平台大类名称"
          name="name"
          :rules="[
            { required: true },
            {
              validator: (_rule, value) => validateStr(_rule, value, 50),
              message: '输入内容不可超过50字符',
            },
          ]"
        >
          <a-input id="name" v-model:value="editForm.name" placeholder="请输入平台大类名称" />
        </a-form-item>
        <a-form-item label="品牌授权" name="brand_authorization">
          <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="editForm.brand_authorization" placeholder="请选择">
            <a-select-option :value="1">是</a-select-option>
            <a-select-option :value="0">否</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-switch id="status" v-model:checked="[false, true][editForm.status]" @click="startStopForm()" checked-children="启用" un-checked-children="停用" />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button style="margin-right: 0.8333rem" type="primary" @click="tapAddRoleSubmit">确认</a-button>
        <a-button @click="isAddRole = false">取消</a-button>
      </template>
    </a-drawer>
    <!-- 查看 -->
    <detail-drawer ref="detailDrawerRef" />
    <a-modal :zIndex="1000" v-model:open="visibleData.isShow" :title="visibleData.title">
      <div class="modalContent">{{ visibleData.content }}</div>
      <template #footer>
        <a-button id="roleManagementFormComfirm" v-if="visibleData.isConfirmBtn" style="margin-right: 0.8333rem" type="primary" @click="visibleData.okFn" :danger="visibleData.okType === 'danger'">
          {{ visibleData.confirmBtnText }}
        </a-button>
        <a-button id="roleManagementFormCancel" v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
      </template>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import Form from '@/components/Form.vue'
import { PageType } from '@/common/enum'

import { Add, Delete, GetList, Update, UpdateStatus } from '@/servers/PlatformLarge'
import { validateStr } from '@/utils/index'
import { message } from 'ant-design-vue'
import { onMounted } from 'vue'
import DetailDrawer from './components/DetailDrawer.vue'

const isAddRole = ref(false)
// const showFilter = ref(false)

const { btnPermission } = usePermission()

const roleModuleType = ref('add')
// 查看
const detailDrawerRef = ref()
const oldStatus = ref(null)
const visibleData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {
    visibleData.isShow = false
  },
})
const formArr: any = ref([
  {
    label: '搜索平台大类名称',
    value: null,
    type: 'input',
    key: 'name',
  },
  {
    label: '品牌授权是否可选',
    value: null,
    type: 'select',
    selectArr: [
      { label: '是', value: 1 },
      { label: '否', value: 0 },
    ],
    key: 'brand_authorization',
  },
  {
    label: '状态',
    value: null,
    type: 'select',
    selectArr: [
      { label: '启用', value: 1 },
      { label: '停用', value: 0 },
    ],
    key: 'status',
  },
])
const editForm = reactive({
  id: null,
  name: '',
  role_code: '',
  scope: 1,
  status: 1,
  is_def: false,
  brand_authorization: null as any,
})
const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.PLATFORM_LARGE_CATEGORIES) {
    const arr: any[] = []
    obj.PLATFORM_LARGE_CATEGORIES.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}
onMounted(() => {
  search()
  initScreening()
})

const tapAddRoleSubmit = async () => {
  try {
    await formRef.value.validateFields()
    switch (roleModuleType.value) {
      case 'add':
        addRole()
        break
      case 'compiler':
        upRoleDate()
        break
      default:
        break
    }
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
const tapManipulate = (type: string, row: any = '') => {
  switch (type) {
    case 'add':
      isAddRole.value = true
      roleModuleType.value = 'add'
      editForm.name = ''
      editForm.role_code = ''
      editForm.scope = 1
      editForm.status = 1
      editForm.brand_authorization = null
      break
    case 'compiler':
      oldStatus.value = row.status
      editForm.id = row.id
      editForm.name = row.name
      editForm.scope = row.scope
      editForm.status = row.status
      editForm.brand_authorization = row.brand_authorization ? (row.brand_authorization === '是' ? 1 : 0) : ''
      isAddRole.value = true
      roleModuleType.value = 'compiler'
      console.log(editForm, 'editForm')
      break
    case 'removes':
      visibleData.isShow = true
      visibleData.title = '删除平台大类'
      visibleData.content = `即将删除该平台大类，删除后：
  - 店铺信息维护时将无法选择当前大类
  - 无法申请该平台大类开通新店铺

  确定要删除该平台大类吗？`
      visibleData.confirmBtnText = '确定'
      visibleData.isCancelBtn = true
      visibleData.okType = 'danger'
      visibleData.okFn = () => {
        deleteRole(row.id)
      }
      break
    default:
      break
  }
}
const tapSwitch = (e, row) => {
  if (!row.status) {
    updateRoleStatus({ ids: [row.id], status: e ? 1 : 0 })
  } else {
    visibleData.isShow = true
    visibleData.title = '停用平台大类'
    visibleData.content = `即将停用该平台大类，停用后：
  - 店铺信息维护时将无法选择当前大类
  - 无法申请该平台大类开通新店铺

  确定要停用该平台大类吗？`
    visibleData.confirmBtnText = '确定'
    visibleData.okType = 'danger'
    visibleData.isCancelBtn = true
    visibleData.okFn = () => {
      updateRoleStatus({ ids: [row.id], status: e ? 1 : 0 })
    }
  }
}

const startStopForm = () => {
  if (editForm.status == 1) {
    editForm.status = 0
  } else {
    editForm.status = 1
  }
}

// 详情
const detail = (item) => {
  detailDrawerRef.value?.open(item.id, btnPermission.value[81006])
}

const tableRef = ref()
const formRef = ref()
const search = () => tableRef.value.search()
// const showTableSetting = () => tableRef.value.showTableSetting()
// const tapScreeningShow = () => formRef.value.openScreening()

// 平台大类启用停用
const updateRoleStatus = (obj) => {
  UpdateStatus(obj).then(() => {
    tableRef.value.search()
    visibleData.isShow = false
  })
}
// 新增平台大类
const addRole = () => {
  const obj = JSON.parse(JSON.stringify(editForm))
  Add(obj).then(() => {
    message.success('新增成功')
    isAddRole.value = false
    search()
  })
}
// 编辑平台大类
const upRoleDate = () => {
  const fn = () => {
    const obj = JSON.parse(JSON.stringify(editForm))
    Update(obj).then(() => {
      message.success('修改成功')
      isAddRole.value = false
      search()
    })
  }
  if (editForm.status == 0 && oldStatus.value == 1) {
    // 停用
    visibleData.isShow = true
    visibleData.title = '停用平台大类'
    visibleData.content = `即将停用该平台大类，停用后：
  - 所有使用此平台大类的用户将失去与该平台大类相关的权限。
  - 受影响的用户将无法执行与此平台大类权限相关的操作，直到他们被分配新的平台大类。

确定要停用该平台大类吗？`
    visibleData.confirmBtnText = '确定'
    visibleData.isCancelBtn = true
    visibleData.okType = 'danger'
    visibleData.okFn = () => {
      visibleData.isShow = false
      fn()
    }
  } else {
    fn()
  }
}
// 删除平台大类
const deleteRole = (id) => {
  Delete({ id })
    .then(() => {
      visibleData.isShow = false
      message.success('删除成功')
      search()
    })
    .catch(() => {
      visibleData.isShow = false
    })
}
</script>
<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .breadcrumbBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 2.5rem;
    margin: 0.6667rem 0;

    .title {
      font-size: 1.3333rem;
      font-weight: bold;
      color: #000;
    }
  }

  .btnlist {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    margin-top: 1.25rem;
  }

  .formBox {
    .operatingAreaBox {
      display: flex;
      align-items: center;
      height: 2.6667rem;

      .tag {
        padding: 0 0.8333rem;
        cursor: pointer;
        user-select: none;
      }

      .btn {
        margin-right: 0.8333rem;
      }
    }
  }


}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 8.3333rem;
    min-width: 8.3333rem;
    margin-right: 2.5rem;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}

.tableBtn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 2.6667rem;
  margin-top: 0.8333rem;
  margin-bottom: 0.8333rem;
  font-size: 1.3333rem;
  font-weight: bold;
  color: #000;
}

.vxe-icon {
  width: 1.3333rem;
  height: 1.3333rem;
}
</style>
