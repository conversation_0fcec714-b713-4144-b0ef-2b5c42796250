<template>
  <a-drawer
    :footer="drawerVisible ? undefined : false"
    v-model:open="drawerVisible"
    width="520"
    :title="formType == 'add' ? '添加紫鸟设备账号' : '编辑紫鸟设备账号'"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
    @afterOpenChange="formRef.clearValidate()"
    @close="onClose"
  >
    <div class="detailBox">
      <LoadingOutlined v-show="loading" class="loadingIcon" />
      <a-form ref="formRef" :model="editForm" :rules="rules">
        <a-form-item label="紫鸟企业" name="purple_bird_primary_account_id">
          <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="editForm.purple_bird_primary_account_id">
            <a-select-option v-for="item in accountList" :key="item.id" :value="item.id">{{ item.name }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="设备名称" name="name">
          <a-input v-model:value="editForm.name" placeholder="请输入紫鸟设备名称" />
        </a-form-item>
        <a-form-item label="设备费用" name="equipment_cost">
          <a-input-number v-model:value="editForm.equipment_cost" placeholder="请输入紫鸟设备费用" :min="0" :max="999999" :precision="2" />
        </a-form-item>
        <a-form-item label="开通时间" name="opening_time">
          <a-date-picker v-model:value="editForm.opening_time" valueFormat="YYYY-MM-DD" />
        </a-form-item>
        <a-form-item label="到期时间" name="expiration_time">
          <a-date-picker v-model:value="editForm.expiration_time" valueFormat="YYYY-MM-DD" />
        </a-form-item>
      </a-form>
    </div>
    <template #footer>
      <a-button style="margin-right: 0.8333rem" type="primary" @click="onSubmit">确认</a-button>
      <a-button @click="onClose">取消</a-button>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { GetDeviceDetail, AddDevice, UpdateDevice, GetPurpleBirdCompanyList } from '@/servers/PurpleBird'
import { LoadingOutlined } from '@ant-design/icons-vue'
import { validateStr } from '@/utils/index'
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'

const emit = defineEmits(['updateStatus', 'success'])
const drawerVisible = ref(false)
const loading = ref(false)
const formRef = ref()
const formType = ref('edit')
const oldStatus = ref(null)
const editForm = ref<any>({
  id: null,
  purple_bird_primary_account_id: null,
  name: '',
  equipment_cost: '',
})
const accountList = ref<any>([])
const checkTime = (_rule: any, value: any, callback: any) => {
  const openingTime = editForm.value.opening_time
  if (value && openingTime && value < openingTime) {
    callback(new Error('到期时间不能小于开通时间'))
  } else {
    callback()
  }
}
const rules: Record<string, Rule[]> = {
  purple_bird_primary_account_id: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 100),
      message: '输入内容不可超过100字符',
    },
  ],
  name: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 100),
      message: '输入内容不可超过100字符',
    },
  ],
  equipment_cost: [{ required: true, trigger: ['change', 'blur'], message: '请输入设备费用' }],
  opening_time: [{ required: true, trigger: ['change', 'blur'], message: '请选择开通时间' }],
  expiration_time: [
    { required: true, trigger: ['change', 'blur'], message: '请选择到期时间' },
    {
      validator: checkTime,
    },
  ],
}

const onSubmit = async () => {
  try {
    await formRef.value.validateFields()
    switch (formType.value) {
      case 'add':
        addSubmit()
        break
      case 'edit':
        updateSubmit()
        break
      default:
        break
    }
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
// 新增紫鸟设备账号
const addSubmit = () => {
  const obj = JSON.parse(JSON.stringify(editForm.value))
  AddDevice(obj).then(() => {
    message.success('新增成功')
    onClose()
    emit('success')
  })
}
// 编辑紫鸟设备账号
const updateSubmit = () => {
  const obj = JSON.parse(JSON.stringify(editForm.value))
  UpdateDevice(obj).then(() => {
    message.success('修改成功')
    onClose()
    emit('success')
  })
}
const onClose = () => {
  formRef.value.resetFields()
  drawerVisible.value = false
}
const open = (id, type) => {
  drawerVisible.value = true
  formType.value = type
  if (id) {
    loading.value = true
    editForm.value = {}
    GetDeviceDetail({ id })
      .then((res) => {
        editForm.value = res.data
        editForm.value.name = res.data.device_name
        oldStatus.value = res.data.status
        loading.value = false
      })
      .catch(() => {
        loading.value = false
      })
  }
}
onMounted(async () => {
  const res = await GetPurpleBirdCompanyList()
  accountList.value = res.data
})
// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 110px;
    min-width: 110px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.detailTitle {
  padding-left: 12px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #000;
}

.w350 {
  width: 350px;
}

.w250 {
  width: 250px;
}

.w150 {
  width: 150px;
}

.w200 {
  width: 200px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: #********;
  white-space: nowrap;
}

.detailValueDescription {
  font-size: 12px;
  color: #********;
}

.detailBox {
  padding-top: 12px;

  .loadingIcon {
    font-size: 30px;
    color: #1890ff;
  }
}

.ant-input-number {
  width: 100%;
}
</style>
