<template>
  <div class="main">
    <div class="main-content">
      <!-- 左侧角色分组面板 -->
      <RoleGroupLeftPanel
        ref="roleGroupPanelRef"
        :group-list="groupList"
        :selected-group-id="selectedGroupId"
        :loading="groupLoading"
        @select-group="handleSelectGroup"
        @create-group="handleCreateGroup"
        @edit-group="handleEditGroup"
        @delete-group="handleDeleteGroup"
        @search="handleGroupSearch"
      />

      <!-- 右侧内容区域 -->
      <div class="right-content">
        <SearchForm v-model:form="formArr" :page-type="PageType.ROLE_MANAGE" @search="search" @setting="tableRef?.showTableSetting()" />
        <!-- 表格 -->
        <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.ROLE_MANAGE" :get-list="GetList" :form-format="formFormat" :is-index="true" :auto-search="hasRoleManagementPermission">
          <template #left-btn>
            <a-button id="roleManagementIncrease" type="primary" @click="tapManipulate('add')" v-if="btnPermission[31001]">新增角色</a-button>
          </template>
          <template #status="{ row }">
            <a-switch
              :disabled="!btnPermission[31005] || row.is_def"
              class="btn"
              @click="tapSwitch($event, row)"
              v-model:checked="[false, true][row.status]"
              checked-children="启用"
              un-checked-children="停用"
            />
          </template>
          <template #create_at="{ row }">
            <span>{{ row.create_at ? row.create_at.slice(0, 16) : '' }}</span>
          </template>
          <template #update_at="{ row }">
            <span>{{ row.update_at ? row.update_at.slice(0, 16) : '' }}</span>
          </template>
          <template #operate="{ row }">
            <!-- <a-button id="roleManagementDetail" @click="detail(row)" class="mr-10px">查看</a-button> -->
            <a-button type="text" :disabled="!btnPermission[31002]" @click="tapManipulate('compiler', row)">编辑</a-button>
            <a-button type="text" :disabled="!btnPermission[31003]" @click="tapManipulate('permissions', row)">权限</a-button>
            <a-button type="text" :disabled="!btnPermission[31004] || row.is_def" @click="tapManipulate('removes', row)">删除</a-button>
          </template>
        </BaseTable>

        <a-drawer
          v-model:open="isAddRole"
          @afterOpenChange="formRef.clearValidate()"
          width="520"
          :title="roleModuleType == 'add' ? '新建角色' : '编辑角色'"
          placement="right"
          :maskClosable="false"
          :footer-style="{ textAlign: 'left' }"
        >
          <a-form ref="formRef" :model="addRoleData">
            <a-form-item
              label="角色名称"
              name="role_name"
              :rules="[
                { required: true },
                {
                  validator: (_rule, value) => validateStr(_rule, value, 50),
                  message: '输入内容不可超过50字符',
                },
              ]"
            >
              <a-input id="role_name" v-model:value="addRoleData.role_name" placeholder="请输入角色名称" @input="(e) => (addRoleData.role_name = e.target.value.replace(/\s+/g, ''))" />
            </a-form-item>

            <!-- <a-form-item label="角色编码" name="role_code" v-if="roleModuleType == 'add'">
          <a-input id="role_code" v-model:value="addRoleData.role_code" placeholder="请输入角色编码" />
        </a-form-item> -->

            <a-form-item label="所属分组" name="group_id">
              <a-select id="group_id" v-model:value="addRoleData.group_id" placeholder="请选择所属分组" allowClear>
                <a-select-option v-for="group in groupList.filter((g) => g.id !== null && Number(g.id) > 0)" :key="group.id" :value="group.id">
                  {{ group.group_name }}
                </a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="状态" name="status">
              <a-switch id="status" :disabled="addRoleData.is_def" v-model:checked="[false, true][addRoleData.status]" @click="startStopForm()" checked-children="启用" un-checked-children="停用" />
              <!-- <a-radio-group v-model:value="addRoleData.status">
            <a-radio :value="1">启用</a-radio>
            <a-radio :value="0">停用</a-radio>
          </a-radio-group> -->
            </a-form-item>
          </a-form>
          <template #footer>
            <a-button style="margin-right: 0.8333rem" type="primary" @click="tapAddRoleSubmit">确认</a-button>
            <a-button @click="isAddRole = false">取消</a-button>
          </template>
        </a-drawer>
        <!-- 查看 -->
        <detail-drawer ref="detailDrawerRef" />
        <a-modal :zIndex="1000" v-model:open="visibleData.isShow" :title="visibleData.title">
          <div class="modalContent">{{ visibleData.content }}</div>
          <template #footer>
            <a-button id="roleManagementFormComfirm" v-if="visibleData.isConfirmBtn" style="margin-right: 0.8333rem" type="primary" @click="visibleData.okFn" :danger="visibleData.okType === 'danger'">
              {{ visibleData.confirmBtnText }}
            </a-button>
            <a-button id="roleManagementFormCancel" v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
          </template>
        </a-modal>
        <!-- 权限 -->
        <EditLimits ref="editLimitsRef" @query="search"></EditLimits>

        <!-- 分组编辑弹窗 -->
        <EditGoupDrawer ref="editGoupDrawerRef" @addGroup="handleGroupUpdated" />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { PageType } from '@/common/enum'
import { Add, Delete, GetList, Update, UpdateRoleStatus, GetRoleGroupList, DeleteRoleGroup } from '@/servers/RoleNew'
import { validateStr, checkPagePermission, buttonDebounce, checkFormParams } from '@/utils/index'
import { usePermission } from '@/hook/usePermission'
import { message } from 'ant-design-vue'
import { onMounted, reactive, ref } from 'vue'
import DetailDrawer from './components/DetailDrawer.vue'
import EditLimits from './components/PermissionDrawer.vue'
import RoleGroupLeftPanel from './components/RoleGroupLeftPanel.vue'
import EditGoupDrawer from './components/EditGoupDrawer.vue'

const isAddRole = ref(false)
const editLimitsRef = ref()
const roleGroupPanelRef = ref()
const editGoupDrawerRef = ref()

const { btnPermission } = usePermission()

// 检查角色管理权限
const hasRoleManagementPermission = ref(false)
// 1. 定义角色分组接口
interface RoleGroup {
  id: string | number
  group_name: string
  role_count: number
  create_at?: string
  [key: string]: any
}
// 角色分组相关数据
const groupList = ref<RoleGroup[]>([])
const selectedGroupId = ref(null)
const groupLoading = ref(false)

const roleModuleType = ref('add')
// 查看
const detailDrawerRef = ref()
const oldStatus = ref(null)
const visibleData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {
    visibleData.isShow = false
  },
})
const formArr: any = ref([
  {
    label: '搜索角色名称',
    value: null,
    type: 'input',
    key: 'role_name',
  },
  {
    label: '更新人',
    value: null,
    type: 'input',
    key: 'modifier',
  },
  {
    label: '状态',
    value: null,
    type: 'select',
    options: [
      { label: '启用', value: 1 },
      { label: '停用', value: 0 },
    ],
    key: 'status',
  },
  // {
  //   label: '所属分组',
  //   value: null,
  //   type: 'select',
  //   options: [],
  //   key: 'group_id',
  // },
  // {
  //   label: '创建时间',
  //   value: null,
  //   type: 'range-picker',
  //   options: [],
  //   key: 'create_at',
  //   formKeys: ['start_time', 'end_time'],
  //   placeholder: ['创建开始时间', '创建结束时间'],
  // },
  {
    label: '修改时间',
    value: null,
    type: 'range-picker',
    options: [],
    key: 'update_at',
    formKeys: ['update_start_time', 'update_end_time'],
    placeholder: ['更新开始时间', '更新结束时间'],
  },
])
const addRoleData = reactive({
  id: null,
  role_name: '',
  role_code: '',
  scope: 1,
  status: 1,
  is_def: false,
  group_id: null,
})
const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.ROLE_MANAGE) {
    const arr: any[] = []
    obj.ROLE_MANAGE.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}
onMounted(() => {
  // 检查当前用户是否有权限访问角色管理页面
  hasRoleManagementPermission.value = checkPagePermission('/roleManagement')

  if (hasRoleManagementPermission.value) {
    loadRoleGroups()
    search()
    initScreening()
  } else {
    console.warn('用户无权限访问角色管理页面，跳过接口调用')
  }
})

const tapAddRoleSubmitCore = async () => {
  try {
    await formRef.value.validateFields()
    switch (roleModuleType.value) {
      case 'add':
        addRole()
        break
      case 'compiler':
        upRoleDate()
        break
      default:
        break
    }
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}

// 添加防抖的角色提交函数
const tapAddRoleSubmit = buttonDebounce(tapAddRoleSubmitCore, 1000)
const tapManipulateCore = (type: string, row: any = '') => {
  switch (type) {
    case 'add':
      isAddRole.value = true
      roleModuleType.value = 'add'
      addRoleData.role_name = ''
      addRoleData.role_code = ''
      addRoleData.scope = 1
      addRoleData.status = 1
      addRoleData.is_def = false
      addRoleData.group_id = null
      break
    case 'compiler':
      oldStatus.value = row.status
      addRoleData.is_def = row.is_def
      addRoleData.id = row.id
      addRoleData.role_name = row.role_name
      addRoleData.scope = row.scope
      addRoleData.status = row.status
      addRoleData.group_id = row.group_id && Number(row.group_id) > 0 ? row.group_id : null
      isAddRole.value = true
      roleModuleType.value = 'compiler'
      break
    case 'permissions':
      openEditLimits(row)
      break
    case 'removes':
      visibleData.isShow = true
      visibleData.title = '删除角色'
      visibleData.content = `即将删除该角色，删除后：
  - 现有系统权限配置可能会受到影响，请确保已经调整了相关权限设置。

请在执行此操作前确认：
  - 已检查并调整系统中与该角色相关的权限配置。
  - 已通知受影响的用户，且已重新分配必要的角色或权限。

此操作不可恢复，确定要删除该角色吗？`
      visibleData.confirmBtnText = '确定'
      visibleData.isCancelBtn = true
      visibleData.okType = 'danger'
      visibleData.okFn = () => {
        deleteRole(row.id)
      }
      break
    default:
      break
  }
}

// 添加防抖的角色操作入口函数
const tapManipulate = buttonDebounce(tapManipulateCore, 300)

const tapSwitch = (e, row) => {
  if (!row.status) {
    updateRoleStatus({ id: row.id, status: e ? 1 : 0 })
  } else {
    visibleData.isShow = true
    visibleData.title = '停用角色'
    visibleData.content = `即将停用该角色，停用后：
  - 所有使用此角色的用户将失去与该角色相关的权限。
  - 受影响的用户将无法执行与此角色权限相关的操作，直到他们被分配新的角色。

确定要停用该角色吗？`
    visibleData.confirmBtnText = '确定'
    visibleData.okType = 'danger'
    visibleData.isCancelBtn = true
    visibleData.okFn = () => {
      updateRoleStatus({ id: row.id, status: e ? 1 : 0 })
    }
  }
}

const startStopForm = () => {
  if (addRoleData.status == 1) {
    addRoleData.status = 0
  } else {
    addRoleData.status = 1
  }
}

const tableRef = ref()
const formRef = ref()
const search = () => {
  tableRef.value.search() // 原角色列表查询
  loadRoleGroups() // 新增：同步查询分组列表
}
// const showTableSetting = () => tableRef.value.showTableSetting()
// const tapScreeningShow = () => formRef.value.openScreening()

// 角色启用停用
const updateRoleStatus = (obj) => {
  UpdateRoleStatus(obj)
    .then(() => {
      tableRef.value.search()
      visibleData.isShow = false
      loadRoleGroups()
    })
    .catch((error: any) => {
      visibleData.isShow = false
      const errorMessage = error?.message || '状态切换失败'
      message.error(errorMessage)
    })
}
// 新增角色
const addRole = () => {
  const obj = JSON.parse(JSON.stringify(addRoleData))
  Add(obj).then(() => {
    message.success('新增成功')
    isAddRole.value = false
    search()
    loadRoleGroups()
  })
}
// 编辑角色
const upRoleDate = () => {
  const fn = () => {
    const obj = JSON.parse(JSON.stringify(addRoleData))
    Update(obj)
      .then(() => {
        message.success('修改成功')
        isAddRole.value = false
        search()
        loadRoleGroups()
      })
      .catch((error: any) => {
        const errorMessage = error?.message || '修改失败'
        message.error(errorMessage)
      })
  }
  if (addRoleData.status == 0 && oldStatus.value == 1) {
    // 停用
    visibleData.isShow = true
    visibleData.title = '停用角色'
    visibleData.content = `即将停用该角色，停用后：
  - 所有使用此角色的用户将失去与该角色相关的权限。
  - 受影响的用户将无法执行与此角色权限相关的操作，直到他们被分配新的角色。

确定要停用该角色吗？`
    visibleData.confirmBtnText = '确定'
    visibleData.isCancelBtn = true
    visibleData.okType = 'danger'
    visibleData.okFn = () => {
      visibleData.isShow = false
      fn()
    }
  } else {
    fn()
  }
}
// 删除角色核心逻辑
const deleteRoleCore = (id) => {
  Delete({ id })
    .then(() => {
      visibleData.isShow = false
      message.success('删除成功')
      search()
      loadRoleGroups()
    })
    .catch((error: any) => {
      visibleData.isShow = false
      const errorMessage = error?.message || '删除失败'
      message.error(errorMessage)
    })
}

// 添加防抖的删除角色函数
const deleteRole = buttonDebounce(deleteRoleCore, 1000)
// 打开权限弹窗核心逻辑
const openEditLimitsCore = (item) => {
  editLimitsRef.value.opendrawer(item)
}

// 添加防抖的打开权限弹窗函数
const openEditLimits = buttonDebounce(openEditLimitsCore, 500)

// 表单格式化 - 添加分组筛选
const formFormat = (obj) => {
  if (selectedGroupId.value !== null && selectedGroupId.value !== undefined && selectedGroupId.value !== 'all') {
    obj.group_id = selectedGroupId.value
  }
  return obj
}

// 加载角色分组列表
const loadRoleGroups = async () => {
  try {
    groupLoading.value = true
    // 构建和 GetList 一样的参数结构
    const obj: any = {}
    obj.page = 1
    obj.pageSize = 9999

    // 使用和表格一样的参数处理逻辑
    const params = checkFormParams({ formArr: formArr.value, obj })

    // 应用表单格式化（添加分组筛选等）
    const formattedParams = formFormat(params)
    const res = await GetRoleGroupList(formattedParams)
    groupList.value = res.data || []

    // 更新搜索表单的分组选项
    const groupFormItem = formArr.value.find((item) => item.key === 'group_id')
    if (groupFormItem) {
      groupFormItem.options = groupList.value.map((group) => ({
        label: group.group_name,
        value: group.id,
      }))
    }
  } catch (error: any) {
    console.error('加载角色分组失败:', error)
    const errorMessage = error?.message || '加载角色分组失败'
    message.error(errorMessage)
  } finally {
    groupLoading.value = false
  }
}

// 选择分组
const handleSelectGroup = (groupId) => {
  selectedGroupId.value = groupId
  search()
}

// 新增分组
const handleCreateGroup = () => {
  editGoupDrawerRef.value?.open()
}

// 编辑分组
const handleEditGroup = (group) => {
  editGoupDrawerRef.value?.open({
    key: group.id,
    label: group.group_name,
  })
}

// 删除分组
const handleDeleteGroup = (group) => {
  visibleData.isShow = true
  visibleData.title = '删除分组'
  visibleData.content = `确定要删除分组"${group.group_name}"吗？如果分组下存在角色不允许删除。`
  visibleData.confirmBtnText = '确定'
  visibleData.okType = 'danger'
  visibleData.isCancelBtn = true
  visibleData.okFn = async () => {
    try {
      const params = {
        ids: group.id.toString(),
        id_list: [group.id],
      }
      await DeleteRoleGroup(params)
      message.success('删除成功')
      visibleData.isShow = false
      loadRoleGroups()
      search()
    } catch (error: any) {
      visibleData.isShow = false
      const errorMessage = error?.message || '删除分组失败'
      // message.error(errorMessage)
      console.error('删除分组失败:', error)
    }
  }
}

// 搜索分组
const handleGroupSearch = (value) => {
  // 这里可以实现分组搜索逻辑
  console.log('搜索分组:', value)
}

// 分组更新回调
const handleGroupUpdated = () => {
  loadRoleGroups()
  search()
}
</script>
<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .main-content {
    display: flex;
    flex: 1;
    height: 100%;
    overflow: hidden;

    .right-content {
      display: flex;
      flex: 1;
      flex-direction: column;
      padding-left: 16px;
      overflow: hidden;
    }
  }

  .breadcrumbBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 2.5rem;
    margin: 0.6667rem 0;

    .title {
      font-size: 1.3333rem;
      font-weight: bold;
      color: #000;
    }
  }

  .btnlist {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    margin-top: 1.25rem;
  }

  .formBox {
    .operatingAreaBox {
      display: flex;
      align-items: center;
      height: 2.6667rem;

      .tag {
        padding: 0 0.8333rem;
        cursor: pointer;
        user-select: none;
      }

      .btn {
        margin-right: 0.8333rem;
      }
    }
  }
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 8.3333rem;
    min-width: 8.3333rem;
    margin-right: 2.5rem;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}

.tableBtn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 2.6667rem;
  margin-top: 0.8333rem;
  margin-bottom: 0.8333rem;
  font-size: 1.3333rem;
  font-weight: bold;
  color: #000;
}

.vxe-icon {
  width: 1.3333rem;
  height: 1.3333rem;
}
</style>
