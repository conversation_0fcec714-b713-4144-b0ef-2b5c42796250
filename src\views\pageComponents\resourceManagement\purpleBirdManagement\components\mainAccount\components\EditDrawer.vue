<template>
  <a-drawer
    :footer="drawerVisible ? undefined : false"
    v-model:open="drawerVisible"
    width="520"
    :title="formType == 'add' ? '添加紫鸟主账号' : '编辑紫鸟主账号'"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
    @afterOpenChange="formRef.clearValidate()"
    @close="onClose"
  >
    <div class="detailBox">
      <LoadingOutlined v-show="loading" class="loadingIcon" />
      <a-form ref="formRef" :model="editForm" :rules="rules">
        <a-form-item label="紫鸟企业" name="name">
          <a-input v-model:value="editForm.name" placeholder="请输入紫鸟企业名" />
        </a-form-item>
        <a-form-item label="紫鸟主账号" name="account_number">
          <a-input v-model:value="editForm.account_number" placeholder="请输入紫鸟主账号" />
        </a-form-item>
        <a-form-item label="紫鸟密码" name="password">
          <a-input-password v-model:value="editForm.password" placeholder="请输入紫鸟密码" v-model:visible="pwdShow" />
        </a-form-item>
      </a-form>
    </div>
    <template #footer>
      <a-button style="margin-right: 0.8333rem" type="primary" @click="onSubmit">确认</a-button>
      <a-button @click="onClose">取消</a-button>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { GetMainAccountDetail, AddMainAccount, UpdateMainAccount } from '@/servers/PurpleBird'
import { LoadingOutlined } from '@ant-design/icons-vue'
import { validateStr } from '@/utils/index'
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'

const emit = defineEmits(['updateStatus', 'success'])
const drawerVisible = ref(false)
const loading = ref(false)
const formRef = ref()
const formType = ref('edit')
const editForm = ref<any>({
  id: null,
  name: '',
  account_number: '',
  password: '',
})
const pwdShow = ref(false)

const rules: Record<string, Rule[]> = {
  name: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 100),
      message: '输入内容不可超过100字符',
    },
  ],
  account_number: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 50),
      message: '输入内容不可超过50字符',
    },
  ],
  password: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 50),
      message: '输入内容不可超过50字符',
    },
  ],
}

const onSubmit = async () => {
  try {
    await formRef.value.validateFields()
    switch (formType.value) {
      case 'add':
        addSubmit()
        break
      case 'edit':
        updateSubmit()
        break
      default:
        break
    }
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
// 新增紫鸟主账号
const addSubmit = () => {
  const obj = JSON.parse(JSON.stringify(editForm.value))
  AddMainAccount(obj).then(() => {
    message.success('新增成功')
    onClose()
    emit('success')
  })
}
// 编辑紫鸟主账号
const updateSubmit = () => {
  const obj = JSON.parse(JSON.stringify(editForm.value))
  UpdateMainAccount(obj).then(() => {
    message.success('修改成功')
    onClose()
    emit('success')
  })
}
const onClose = () => {
  formRef.value.resetFields()
  drawerVisible.value = false
  pwdShow.value = false
}
const open = (id, type) => {
  drawerVisible.value = true
  formType.value = type
  if (id) {
    loading.value = true
    editForm.value = {}
    GetMainAccountDetail({ id, is_plaintext: true })
      .then((res) => {
        editForm.value = res.data
        loading.value = false
      })
      .catch(() => {
        loading.value = false
      })
  }
}
// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 110px;
    min-width: 110px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.detailTitle {
  padding-left: 12px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #000;
}

.w350 {
  width: 350px;
}

.w250 {
  width: 250px;
}

.w150 {
  width: 150px;
}

.w200 {
  width: 200px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: #********;
  white-space: nowrap;
}

.detailValueDescription {
  font-size: 12px;
  color: #********;
}

.detailBox {
  padding-top: 12px;

  .loadingIcon {
    font-size: 30px;
    color: #1890ff;
  }
}

.ant-input-number {
  width: 100%;
}
</style>
