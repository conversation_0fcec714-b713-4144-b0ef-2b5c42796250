import { request } from './request'
// 获取列表
export const GetList = (data) => request({ url: '/api/PlatformSubCategory/GetList', data })
// 新建
export const Add = (data) => request({ url: '/api/PlatformSubCategory/Add', data })
// 查看
export const Details = (data) => request({ url: '/api/PlatformSubCategory/Details', data })
// 编辑
export const Update = (data) => request({ url: '/api/PlatformSubCategory/Update', data })
// 修改状态
export const UpdateStatus = (data) => request({ url: '/api/PlatformSubCategory/UpdateStatus', data })
// 删除
export const Delete = (data) => request({ url: '/api/PlatformSubCategory/Delete', data })
// 日志
export const GetOpLogInfos = (data) => request({ url: '/api/PlatformSubCategory/GetOpLogInfos', data })
