import type { InputProps, InputNumberProps, SelectProps, TimeRangePickerProps, TreeSelectProps, DatePickerProps } from 'ant-design-vue'

export interface QuickItem {
  label: string
  valueMap: Record<string, any>
}

// 公共基础类型
type FormCommonProps = {
  label: string
  key: string
  value?: any
  isShow?: boolean
  isQuicks?: boolean
  showTooltip?: boolean
}

interface FormItemTypeMap {
  input: InputProps
  select: SelectProps
  'range-picker': DatePickerProps
  'select-one': SelectProps
  'select-tree': TreeSelectProps
  date: TimeRangePickerProps
  'batch-input': InputProps
  'range-input': InputNumberProps
}

export type FormItemType<T extends keyof FormItemTypeMap> = {
  type: T
} & FormCommonProps &
  Omit<FormItemTypeMap[T], 'type'>

// 导出联合类型
export type FormItem = {
  [K in keyof FormItemTypeMap]: FormItemType<K>
}[keyof FormItemTypeMap]
