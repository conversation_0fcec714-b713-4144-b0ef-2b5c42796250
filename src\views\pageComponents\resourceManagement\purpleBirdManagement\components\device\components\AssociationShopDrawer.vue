<template>
  <a-drawer v-model:open="visible" width="40vw" title="查看设备关联店铺" placement="right" :maskClosable="false" :footer-style="{ textAlign: 'left' }">
    <div class="detailBox">
      <LoadingOutlined v-show="loading" class="loadingIcon" />
      <a-table :columns="columns" :data-source="target" :pagination="{ hideOnSinglePage: true }"></a-table>
    </div>
  </a-drawer>
</template>

<script lang="ts" setup>
import { LoadingOutlined } from '@ant-design/icons-vue'
import { GetBindStore } from '@/servers/PurpleBird'

const visible = ref(false)
const loading = ref(false)

const columns = [
  {
    title: '店铺名称',
    dataIndex: 'shop_name',
  },
  {
    title: '平台小类',
    dataIndex: 'shop_platform_subtype',
  },
]
const target = ref<any>(null)
const open = (id) => {
  target.value = null
  loading.value = true
  visible.value = true
  GetBindStore({ id })
    .then((res) => {
      target.value = res.data
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}
// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 110px;
    min-width: 110px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.detailTitle {
  padding-left: 12px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #000;
}

.w350 {
  width: 350px;
}

.w250 {
  width: 250px;
}

.w150 {
  width: 150px;
}

.w200 {
  width: 200px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: #00000080;
  white-space: nowrap;
}

.detailValueDescription {
  font-size: 12px;
  color: #00000080;
}

.detailBox {
  padding-top: 12px;

  .loadingIcon {
    font-size: 30px;
    color: #1890ff;
  }
}
</style>
