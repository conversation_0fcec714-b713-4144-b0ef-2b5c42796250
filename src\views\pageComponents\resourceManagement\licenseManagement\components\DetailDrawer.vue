<template>
  <a-drawer
    :footer="logVisble ? undefined : false"
    v-model:open="detailVisible"
    :width="appStore.isOpenLog ? '91vw' : '45vw'"
    title="查看"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
    :bodyStyle="{ padding: '0' }"
  >
    <template #extra v-if="logVisble">
      <a-button @click="changeLogVisible">日志</a-button>
    </template>
    <div class="detailAllBox">
      <LoadingOutlined v-show="detailloading" class="loadingIcon" />
      <a-form v-if="!detailloading && target">
        <a-collapse v-model:activeKey="activeKey" :bordered="true" expandIconPosition="end" ghost>
          <template #expandIcon="{ isActive }">
            <DoubleRightOutlined :rotate="isActive ? 270 : 90" />
          </template>

          <a-collapse-panel key="1" header="执照" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="8">
                <p class="label">执照</p>
                <p class="value">{{ target.license_name }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">执照类型</p>
                <p class="value">{{ target.license_type }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">归属分部</p>
                <p class="value">{{ target.affiliated_division }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">附件</p>
                <a-space>
                  <span v-if="!target.files" @click="onLook(target)" :class="['look-btn', btnPermission[87005] ? 'blue' : 'forbid']">点击查看</span>
                  <span v-else-if="!target.files.length">--</span>
                  <a v-else v-for="(item, idx) in target.files" :key="idx" class="blue" :href="item" target="_blank">营业执照（副本）</a>
                </a-space>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">状态</p>
                <p class="value">{{ target.status == 0 ? '停用' : '启用' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">数据来源</p>
                <p class="value">{{ target.source_type == 1 ? 'OA' : '' }}</p>
              </a-col>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel key="2" header="其他" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="8">
                <p class="label">最后修改时间</p>
                <p class="value">{{ target.update_at }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">最后修改人</p>
                <p class="value">
                  <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
                    <template #title>
                      <div>用户名称：{{ target?.modified_user?.real_name }}</div>
                      <div v-show="target?.modified_user?.scope == 1">所属公司：{{ target?.modified_user?.sub_company_name ? target?.modified_user?.sub_company_name : '--' }}</div>
                      <div v-show="target?.modified_user?.scope != 1">所属客户：{{ target?.modified_user?.update_user_customer_name ? target?.modified_user?.update_user_customer_name : '--' }}</div>
                      <div>所在部门：{{ target?.modified_user?.department ? target?.modified_user?.department : '--' }}</div>
                      <div>
                        岗
                        <span style="visibility: hidden">占位</span>
                        位：{{ target?.modified_user?.job_name ? target?.modified_user?.job_name : '--' }}
                      </div>
                    </template>
                    <div style="display: flex; align-items: center">
                      <span class="detailValue">{{ target?.modified_user?.real_name ? target?.modified_user?.real_name : '--' }}</span>
                      <span v-if="target?.modified_user?.department || target?.modified_user?.job_name" class="detailValueDescription">
                        （
                        <span v-if="target?.modified_user?.job_name">{{ target?.modified_user?.job_name }}&nbsp;|&nbsp;</span>
                        <span v-if="target?.modified_user?.department">
                          {{ target?.modified_user?.department.length > 10 ? target?.modified_user?.department.slice(0, 10) + '...' : target?.modified_user?.department }}
                        </span>
                        ）
                      </span>
                    </div>
                  </a-tooltip>
                </p>
              </a-col>
            </a-row>
          </a-collapse-panel>
        </a-collapse>
      </a-form>
      <log-drawer ref="logDrawerRef" class="log" v-if="!detailloading && appStore.isOpenLog" />
    </div>
  </a-drawer>
  <!-- 日志 -->
</template>

<script lang="ts" setup>
// import { Detail } from '@/servers/Role'
import { Details, GetLicenseAttachments } from '@/servers/LicenseMana'
import { LoadingOutlined, DoubleRightOutlined } from '@ant-design/icons-vue'
import useAppStore from '@/store/modules/app'
import LogDrawer from './LogDrawer.vue'

const { btnPermission } = usePermission()
const activeKey = ref(['1', '2'])
const customStyle = 'overflow: hidden; border: .0625rem solid #eee; margin-bottom: 1.25rem;'

const appStore = useAppStore()

const logDrawerRef = ref()
const detailVisible = ref(false)
const detailloading = ref(false)
const logVisble = ref(false)

const target = ref<any>(null)
const open = (id, boolean) => {
  console.log(boolean)

  target.value = null
  detailloading.value = true
  detailVisible.value = true
  logVisble.value = boolean
  Details({ id })
    .then((res) => {
      target.value = res.data
      detailloading.value = false
      nextTick(() => {
        logDrawerRef.value?.open(target.value)
      })
    })
    .catch(() => {
      detailloading.value = false
    })
}
const changeLogVisible = () => {
  appStore.changeLogOpenVisible(appStore.isOpenLog ? 0 : 1)
  if (appStore.isOpenLog) {
    nextTick(() => {
      logDrawerRef.value?.open(target.value)
    })
  }
}
const onLook = (item) => {
  if (!btnPermission.value[87005]) return
  GetLicenseAttachments({ id: item.id }).then((res) => {
    item.files = res.data || []
  })
}
// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
:deep(.ant-collapse-header) {
  background-color: #f4f7fe;
}

.look-btn {
  cursor: pointer;
}

.blue {
  color: #1890ff;
}

.grey {
  color: #aaa;
}

.forbid {
  cursor: not-allowed;
}
</style>
