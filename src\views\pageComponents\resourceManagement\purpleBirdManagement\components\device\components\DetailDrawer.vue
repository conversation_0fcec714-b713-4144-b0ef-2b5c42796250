<template>
  <a-drawer
    :footer="logVisble ? undefined : false"
    v-model:open="detailVisible"
    :width="appStore.isOpenLog ? '91vw' : '45vw'"
    title="查看紫鸟设备账号"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
    :bodyStyle="{ padding: '0' }"
  >
    <template #extra v-if="logVisble">
      <a-button @click="changeLogVisible">日志</a-button>
    </template>

    <div class="detailAllBox">
      <LoadingOutlined v-show="detailloading" class="loadingIcon" />

      <a-form v-if="!detailloading && target">
        <a-collapse v-model:activeKey="activeKey" :bordered="true" expandIconPosition="end" ghost>
          <template #expandIcon="{ isActive }">
            <DoubleRightOutlined :rotate="isActive ? 270 : 90" />
          </template>

          <a-collapse-panel key="1" header="紫鸟" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="8">
                <p class="label">紫鸟企业</p>
                <p class="value">{{ target.enterprise }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">设备名称</p>
                <p class="value">{{ target.device_name }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">设备费用</p>
                <p class="value">{{ target.equipment_cost }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">开通时间</p>
                <p class="value">{{ target.opening_time }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">到期时间</p>
                <p class="value" :style="{ color: isAdvent(target.expiration_time) ? '#FF4D4F' : '#transparent' }">{{ target.expiration_time }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">关联店铺数</p>
                <p class="value">
                  {{ target.shop_math }} &nbsp;
                  <a-button type="link" @click="onOpenShopDetail" :disabled="!btnPermission[86206]">查看详情</a-button>
                </p>
              </a-col>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel key="2" header="其他" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="8">
                <p class="label">创建时间</p>
                <p class="value">{{ target.create_at }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">创建人</p>
                <p class="value">
                  <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
                    <template #title>
                      <div>用户名称：{{ target.create_user_real_name }}</div>
                      <div v-show="target.create_user_scope == 1">所属公司：{{ target.create_user_company_name ? target.create_user_company_name : '--' }}</div>
                      <div v-show="target.create_user_scope != 1">所属客户：{{ target.create_user_customer_name ? target.create_user_customer_name : '--' }}</div>
                      <div>所在部门：{{ target.create_user_department ? target.create_user_department : '--' }}</div>
                      <div>
                        岗
                        <span style="visibility: hidden">占位</span>
                        位：{{ target.create_user_jobtitlename ? target.create_user_jobtitlename : '--' }}
                      </div>
                    </template>
                    <div style="display: flex; align-items: center">
                      <span class="detailValue">{{ target.create_user_real_name ? target.create_user_real_name : '--' }}</span>
                      <span v-if="target.create_user_department || target.create_user_jobtitlename" class="detailValueDescription">
                        （
                        <span v-if="target.create_user_jobtitlename">{{ target.create_user_jobtitlename }}&nbsp;|&nbsp;</span>
                        <span v-if="target.create_user_department">
                          {{ target.create_user_department.length > 10 ? target.create_user_department.slice(0, 10) + '...' : target.create_user_department }}
                        </span>
                        ）
                      </span>
                    </div>
                  </a-tooltip>
                </p>
              </a-col>

              <a-col class="gutter-row" :span="8">
                <p class="label">最后修改时间</p>
                <p class="value">{{ target.update_at }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">最后修改人</p>
                <p class="value">
                  <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
                    <template #title>
                      <div>用户名称：{{ target.update_user_real_name }}</div>
                      <div v-show="target.update_user_scope == 1">所属公司：{{ target.update_user_company_name ? target.update_user_company_name : '--' }}</div>
                      <div v-show="target.update_user_scope != 1">所属客户：{{ target.update_user_customer_name ? target.update_user_customer_name : '--' }}</div>
                      <div>所在部门：{{ target.update_user_department ? target.update_user_department : '--' }}</div>
                      <div>
                        岗
                        <span style="visibility: hidden">占位</span>
                        位：{{ target.update_user_jobtitlename ? target.update_user_jobtitlename : '--' }}
                      </div>
                    </template>
                    <div style="display: flex; align-items: center">
                      <span class="detailValue">{{ target.update_user_real_name ? target.update_user_real_name : '--' }}</span>
                      <span v-if="target.update_user_department || target.update_user_jobtitlename" class="detailValueDescription">
                        （
                        <span v-if="target.update_user_jobtitlename">{{ target.update_user_jobtitlename }}&nbsp;|&nbsp;</span>
                        <span v-if="target.update_user_department">
                          {{ target.update_user_department.length > 10 ? target.update_user_department.slice(0, 10) + '...' : target.update_user_department }}
                        </span>
                        ）
                      </span>
                    </div>
                  </a-tooltip>
                </p>
              </a-col>
            </a-row>
          </a-collapse-panel>
        </a-collapse>

        <!-- <a-form-item label="紫鸟企业">
          <span class="detailValue">{{ target.enterprise }}</span>
        </a-form-item>
        <a-form-item label="设备名称">
          <span class="detailValue">{{ target.device_name }}</span>
        </a-form-item>
        <a-form-item label="设备费用">
          <span class="detailValue">{{ target.equipment_cost }}</span>
        </a-form-item>
        <a-form-item label="开通时间">
          <span class="detailValue">{{ target.opening_time }}</span>
        </a-form-item>
        <a-form-item label="到期时间">
          <span class="detailValue" :style="{ color: isAdvent(target.expiration_time) ? '#FF4D4F' : '#transparent' }">{{ target.expiration_time }}</span>
        </a-form-item>
        <a-form-item label="关联店铺数">
          <span class="detailValue">{{ target.shop_math }}</span>
          &nbsp;
          <a-button type="link" @click="onOpenShopDetail" :disabled="!btnPermission[86206]">查看详情</a-button>
        </a-form-item>
        <div class="drawer-title">其他信息</div>
        <div style="display: flex">
          <a-form-item label="创建时间">
            <div class="detailValue w200">{{ target.create_at }}</div>
          </a-form-item>
          <a-form-item label="创建人">
            <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
              <template #title>
                <div>用户名称：{{ target.create_user_real_name }}</div>
                <div v-show="target.create_user_scope == 1">所属公司：{{ target.create_user_company_name ? target.create_user_company_name : '--' }}</div>
                <div v-show="target.create_user_scope != 1">所属客户：{{ target.create_user_customer_name ? target.create_user_customer_name : '--' }}</div>
                <div>所在部门：{{ target.create_user_department ? target.create_user_department : '--' }}</div>
                <div>
                  岗
                  <span style="visibility: hidden">占位</span>
                  位：{{ target.create_user_jobtitlename ? target.create_user_jobtitlename : '--' }}
                </div>
              </template>
              <div style="display: flex; align-items: center">
                <span class="detailValue">{{ target.create_user_real_name ? target.create_user_real_name : '--' }}</span>
                <span v-if="target.create_user_department || target.create_user_jobtitlename" class="detailValueDescription">
                  （
                  <span v-if="target.create_user_jobtitlename">{{ target.create_user_jobtitlename }}&nbsp;|&nbsp;</span>
                  <span v-if="target.create_user_department">{{ target.create_user_department.length > 10 ? target.create_user_department.slice(0, 10) + '...' : target.create_user_department }}</span>
                  ）
                </span>
              </div>
            </a-tooltip>
          </a-form-item>
        </div>
        <div style="display: flex">
          <a-form-item label="最后修改时间">
            <div class="detailValue w200">{{ target.update_at }}</div>
          </a-form-item>
          <a-form-item label="最后修改人">
            <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
              <template #title>
                <div>用户名称：{{ target.update_user_real_name }}</div>
                <div v-show="target.update_user_scope == 1">所属公司：{{ target.update_user_company_name ? target.update_user_company_name : '--' }}</div>
                <div v-show="target.update_user_scope != 1">所属客户：{{ target.update_user_customer_name ? target.update_user_customer_name : '--' }}</div>
                <div>所在部门：{{ target.update_user_department ? target.update_user_department : '--' }}</div>
                <div>
                  岗
                  <span style="visibility: hidden">占位</span>
                  位：{{ target.update_user_jobtitlename ? target.update_user_jobtitlename : '--' }}
                </div>
              </template>
              <div style="display: flex; align-items: center">
                <span class="detailValue">{{ target.update_user_real_name ? target.update_user_real_name : '--' }}</span>
                <span v-if="target.update_user_department || target.update_user_jobtitlename" class="detailValueDescription">
                  （
                  <span v-if="target.update_user_jobtitlename">{{ target.update_user_jobtitlename }}&nbsp;|&nbsp;</span>
                  <span v-if="target.update_user_department">{{ target.update_user_department.length > 10 ? target.update_user_department.slice(0, 10) + '...' : target.update_user_department }}</span>
                  ）
                </span>
              </div>
            </a-tooltip>
          </a-form-item>
        </div> -->
      </a-form>
      <log-drawer ref="logDrawerRef" name="紫鸟设备账号" :api="GetDeviceLog" :id="target?.id" v-if="!detailloading && appStore.isOpenLog" class="log" />
    </div>
  </a-drawer>
  <!-- 日志 -->
  <!-- 查看关联店铺 -->
  <AssociationShopDrawer ref="shopRef"></AssociationShopDrawer>
</template>

<script lang="ts" setup>
import { LoadingOutlined, DoubleRightOutlined } from '@ant-design/icons-vue'
import { GetDeviceLog, GetDeviceDetail } from '@/servers/PurpleBird'
import useAppStore from '@/store/modules/app'
import LogDrawer from './LogDrawer.vue'
import AssociationShopDrawer from './AssociationShopDrawer.vue'

const activeKey = ref(['1', '2'])
const customStyle = 'overflow: hidden; border: .0625rem solid #eee; margin-bottom: 1.25rem;'

const appStore = useAppStore()
const { btnPermission } = usePermission()
const logDrawerRef = ref()
const detailVisible = ref(false)
const detailloading = ref(false)
const logVisble = ref(false)
const shopRef = ref()
const target = ref<any>(null)
const open = (id, boolean) => {
  target.value = null
  detailloading.value = true
  detailVisible.value = true
  logVisble.value = boolean
  GetDeviceDetail({ id })
    .then((res) => {
      target.value = res.data
      detailloading.value = false
      nextTick(() => {
        logDrawerRef.value?.open(target.value)
      })
    })
    .catch(() => {
      detailloading.value = false
    })
}
const onOpenShopDetail = () => {
  shopRef.value?.open(target.value.id)
}
const changeLogVisible = () => {
  appStore.changeLogOpenVisible(appStore.isOpenLog ? 0 : 1)
  if (appStore.isOpenLog) {
    nextTick(() => {
      logDrawerRef.value?.open(target.value)
    })
  }
}
// const log = () => {
//   logDrawerRef.value?.open(target.value)
// }
const isAdvent = (date) => {
  const now = new Date()
  const end = new Date(date)
  const diff = end.getTime() - now.getTime()
  const diffDays = diff / (1000 * 60 * 60 * 24)
  return diffDays < 3
}
// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
:deep(.ant-collapse-header) {
  background-color: #f4f7fe;
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 110px;
    min-width: 110px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.detailTitle {
  padding-left: 12px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #000;
}

.w350 {
  width: 350px;
}

.w250 {
  width: 250px;
}

.w150 {
  width: 150px;
}

.w200 {
  width: 200px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: #00000080;
  white-space: nowrap;
}

.detailValueDescription {
  font-size: 12px;
  color: #00000080;
}

.detailBox {
  padding-top: 12px;

  .loadingIcon {
    font-size: 30px;
    color: #1890ff;
  }
}
</style>
