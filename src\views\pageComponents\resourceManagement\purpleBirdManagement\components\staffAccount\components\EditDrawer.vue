<template>
  <a-drawer
    :footer="drawerVisible ? undefined : false"
    v-model:open="drawerVisible"
    width="45vw"
    :title="formType == 'add' ? '添加紫鸟员工账号' : '编辑紫鸟员工账号'"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
    @afterOpenChange="formRef.clearValidate()"
    @close="onClose"
  >
    <div class="detailBox">
      <LoadingOutlined v-show="loading" class="loadingIcon" />
      <a-form ref="formRef" :model="editForm" :rules="rules">
        <a-form-item label="紫鸟企业" name="purple_bird_primary_account_id">
          <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="editForm.purple_bird_primary_account_id" placeholder="请选择紫鸟企业">
            <a-select-option v-for="item in accountList" :key="item.id" :value="item.id">{{ item.name }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="员工账号" name="account_number">
          <a-input v-model:value="editForm.account_number" placeholder="请输入紫鸟员工账号" />
        </a-form-item>
        <a-form-item label="紫鸟密码" name="password">
          <a-input-password v-model:value="editForm.password" placeholder="请输入紫鸟密码" v-model:visible="pwdShow" />
        </a-form-item>
        <a-form-item label="负责人" name="principal_id">
          <a-form-item-rest>
            <a-select
              :getPopupContainer="(triggerNode) => triggerNode.parentNode"
              v-model:value="editForm.sub_company_id"
              placeholder="请选择所属公司"
              @change="changeCompany"
              class="short"
              show-search
              :filter-option="(input, option) => filterOption(input, option)"
            >
              <a-select-option v-for="item in companyList" :key="item.value" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
            </a-select>
            <a-tree-select
              show-search
              class="short"
              v-model:value="editForm.department_id"
              allow-clear
              :tree-data="departmentList"
              :fieldNames="{
                children: 'childrenList',
                label: 'departmentname',
                value: 'departmentid',
              }"
              :maxTagCount="1"
              :listHeight="400"
              :dropdownMatchSelectWidth="250"
              placeholder="请选择所在部门"
              @click="onClickDepartment"
              @change="changeDepartment"
              treeNodeFilterProp="departmentname"
              :filter-option="(input, option) => filterOption(input, option)"
            />
          </a-form-item-rest>
          <a-select
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="editForm.principal_id"
            placeholder="请选择负责人"
            @click="onClickPrincipal"
            class="short"
            show-search
            :filter-option="(input, option) => filterOption(input, option)"
          >
            <a-select-option v-for="item in userList" :key="item.id" :value="item.id" :label="item.principal">{{ item.principal }}</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </div>
    <template #footer>
      <a-button style="margin-right: 0.8333rem" type="primary" @click="onSubmit">确认</a-button>
      <a-button @click="onClose">取消</a-button>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { GetStaffDetail, AddStaff, UpdateStaff, GetPurpleBirdCompanyList } from '@/servers/PurpleBird'
import { GetDepartmentTreeList, GetCompanyList, GetUserOptions } from '@/servers/UserManager'
import { LoadingOutlined } from '@ant-design/icons-vue'
import { validateStr, filterOption } from '@/utils/index'
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'

const emit = defineEmits(['updateStatus', 'success'])
const drawerVisible = ref(false)
const loading = ref(false)
const formRef = ref()
const formType = ref('edit')
const editForm = ref<any>({
  id: null,
  purple_bird_primary_account_id: null,
  account_number: '',
  password: '',
  sub_company_id: null,
  department_id: null,
  principal_id: null,
})
const accountList = ref<any>([])
const companyList = ref<any>([])
const departmentList = ref<any>([])
const userList = ref<any>([])
const pwdShow = ref(false)

const rules: Record<string, Rule[]> = {
  purple_bird_primary_account_id: [{ required: true, trigger: ['change', 'blur'], message: '请选择紫鸟企业' }],
  account_number: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 50),
      message: '输入内容不可超过50字符',
    },
  ],
  password: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 50),
      message: '输入内容不可超过50字符',
    },
  ],
  principal_id: [{ required: true, trigger: ['change', 'blur'], message: '请选择负责人' }],
}

const onSubmit = async () => {
  try {
    await formRef.value.validateFields()
    switch (formType.value) {
      case 'add':
        addSubmit()
        break
      case 'edit':
        updateSubmit()
        break
      default:
        break
    }
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
// 新增紫鸟员工账号
const addSubmit = () => {
  const obj = JSON.parse(JSON.stringify(editForm.value))
  AddStaff(obj).then(() => {
    message.success('新增成功')
    onClose()
    emit('success')
  })
}
// 编辑紫鸟员工账号
const updateSubmit = () => {
  const obj = JSON.parse(JSON.stringify(editForm.value))
  UpdateStaff(obj).then(() => {
    message.success('修改成功')
    onClose()
    emit('success')
  })
}
const onClose = () => {
  formRef.value.resetFields()
  editForm.value.sub_company_id = ''
  editForm.value.department_id = ''
  drawerVisible.value = false
  pwdShow.value = false
}
const open = async (id, type) => {
  drawerVisible.value = true
  formType.value = type
  if (id) {
    loading.value = true
    editForm.value = {}
    const res = await GetStaffDetail({ id, is_plaintext: true })
    editForm.value = res.data
    loading.value = false
    getDepartmentTreeList()
    getUserList()
  }
}
const onClickDepartment = () => {
  if (!editForm.value.sub_company_id) {
    message.info('请先选择所属公司')
  }
}
// 获取公司树状下拉框
const getCompanyList = () => {
  GetCompanyList().then((res) => {
    res.data.forEach((x) => {
      x.label = x.company_name
      x.value = x.company_id
    })
    companyList.value = res.data
  })
}

// 获取部门树状下拉框(内部)
const getDepartmentTreeList = () => {
  if (!editForm.value.sub_company_id) {
    return
  }
  GetDepartmentTreeList({ subcompanyid1: editForm.value.sub_company_id }).then((res) => {
    departmentList.value = res.data
  })
}

// 获取用户下拉数据
const getUserList = async () => {
  const res = await GetUserOptions({ subcompanyid1: editForm.value.sub_company_id, departmentid: editForm.value.department_id })
  userList.value = res.data
}

const onClickPrincipal = () => {
  if (!editForm.value.department_id) {
    message.info('请先选择所在部门')
  }
}

const changeCompany = () => {
  getDepartmentTreeList()
  editForm.value.department_id = ''
  editForm.value.principal_id = ''
  userList.value = []
}

const changeDepartment = async () => {
  if (!editForm.value.department_id) {
    return
  }
  editForm.value.principal_id = ''
  getUserList()
}

onMounted(async () => {
  const res = await GetPurpleBirdCompanyList()
  accountList.value = res.data
  getCompanyList()
})
// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 110px;
    min-width: 110px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.detailTitle {
  padding-left: 12px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #000;
}

.w350 {
  width: 350px;
}

.w250 {
  width: 250px;
}

.w150 {
  width: 150px;
}

.w200 {
  width: 200px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: #********;
  white-space: nowrap;
}

.detailValueDescription {
  font-size: 12px;
  color: #********;
}

.detailBox {
  padding-top: 12px;

  .loadingIcon {
    font-size: 30px;
    color: #1890ff;
  }
}

.ant-input-number {
  width: 100%;
}

.short:first-child {
  width: 22rem;
}

.short {
  width: 15.8rem;
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
