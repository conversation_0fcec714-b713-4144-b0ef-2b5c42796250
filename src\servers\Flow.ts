// 店铺管理
import { request } from './request'

// 变更负责人列表
export const GetChangeOwnerList = (data) => {
  return request({ url: '/api/Flow/changeOwnerFlowList', data })
}

// 查看店铺详情
export const GetStoreInfo = (data) => {
  return request({ url: '/api/Flow/ShopDetail', data })
}

// 变更负责人审批详情数据接口（点击审批/处理时候（查看当前节点之前的内容））
export const GetFlowDetail = (data) => {
  return request({ url: '/api/Flow/changeOwnerFlowOperation/Details', data })
}

// 变更负责人审批详情数据接口(用于查看)
export const GetFlowDetailByRead = (data) => {
  return request({ url: '/api/Flow/changeOwnerFlowExamine', data })
}

// 变更负责人审批接口
export const ApprovalChangeOwner = (data) => {
  return request({ url: '/api/Flow/changeOwnerFlowOperation', data })
}

// 流程审批意见
export const GetApprovalOpinion = (data) => {
  return request({ url: '/api/Flow/changeOwnerFlowOpinion', data })
}

// 列表获取待处理审批数量 - 已废弃，暂时返回空数据
export const GetPendingApprovalCount = () => {
  console.warn('GetPendingApprovalCount接口已废弃，请联系后端提供新的替代接口')
  return Promise.resolve({
    data: {
      change_owner_flow_num: 0,
      shop_registration_flow_num: 0,
    },
  })
}

/* 
  店铺注册流程
*/

// 店铺注册流程列表
export const GetStoreRegisterList = (data) => {
  return request({ url: '/api/Flow/shopRegistrationFlowList', data })
}

// 店铺注册审批记录
export const GetStoreRegisterOpinion = (data) => {
  return request({ url: '/api/Flow/shopRegistrationFlowOpinion', data })
}

// 店铺注册详情数据接口(用于查看)
export const GetStoreRegisterDetailByRead = (data) => {
  return request({ url: '/api/Flow/shopRegistrationFlowExamine', data })
}

// 店铺注册详情数据接口（用于审批、处理操作）
export const GetStoreRegisterDetail = (data) => {
  return request({ url: '/api/Flow/shopRegistrationFlow/Details', data })
}

// 店铺注册流审批接口
export const ApprovalStoreRegister = (data) => {
  return request({ url: '/api/Flow/shopRegistrationFlowOperation', data, isFormData: true })
}
