import '@/assets/style/index.scss'
import router from '@/router'

import VxeUI from '@/utils/VxeUi'
import VxeTable from 'vxe-table'
import 'vxe-table/lib/style.css'

import Antd from 'ant-design-vue'
import 'virtual:uno.css'
import { createApp } from 'vue'
import { registerSW } from 'virtual:pwa-register'
import { LoginRedirect } from '@/servers/UmcAuth'
import App from './App.vue'
import 'ant-design-vue/dist/reset.css'
import store from './store'
import { installDirective } from './directive'

const app = createApp(App).use(Antd).use(VxeUI).use(VxeTable).use(router).use(store)

installDirective(app)

// PWA immediate自动更新 & 定时任务更新
const intervalMS = 60 * 60 * 1000 * 12
registerSW({
  immediate: true,
  async onRegisteredSW(swUrl, r) {
    console.info(swUrl, r)

    if (r) {
      const logColor1 = `color: white; padding: 2px 5px; border-radius: 2px;`
      const logColor2 = `font-weight: bold; color: #aaa;`
      const now = Date.now()
      const startTime = localStorage.getItem('ws_start_time')
      if (!startTime) localStorage.setItem('ws_start_time', `${now}`)
      if (startTime && now - Number(startTime) > intervalMS) {
        console.info(`%cUPDATE BEFORE%c 定时任务自动更新`, `${logColor1} background: #faad14`, logColor2)
        if (r.installing || !navigator) return

        if ('connection' in navigator && !navigator.onLine) return

        const resp = await fetch(swUrl, {
          cache: 'no-store',
          headers: {
            cache: 'no-store',
            'cache-control': 'no-cache',
          },
        })

        if (resp?.status === 200) {
          await r.update()
          console.info(`%cUPDATED%c 已更新`, `${logColor1} background: #52c41a`, logColor2)
          localStorage.setItem('ws_start_time', `${now}`)
        }
      }
    }
  },
  onRegisterError: (error) => {
    console.error('registerSW onRegisterError: ', error)
  },
})

// 动态获取当前页面的IP和端口
const getCurrentHostInfo = () => {
  const protocol = window.location.protocol // http: 或 https:
  const hostname = window.location.hostname // IP地址或域名
  const port = window.location.port // 端口号

  // 构建完整的主机信息
  let hostInfo = `${protocol}//${hostname}`
  if (port) {
    hostInfo += `:${port}`
  }

  return hostInfo
}

// 检查退出登录Cookie
const checkLogoutCookie = () => {
  // 检查是否存在退出登录标识Cookie
  // Cookie名称：isLogout，值：true
  const cookies = document.cookie.split(';')
  for (const cookie of cookies) {
    const [name, value] = cookie.trim().split('=')
    if (name === 'isLogout' && value === 'true') {
      if (import.meta.env.VITE_APP_ENV === 'development') {
        console.log('检测到退出登录Cookie:', { name, value })
      }
      return true
    }
  }

  if (import.meta.env.VITE_APP_ENV === 'development') {
    console.log('未检测到退出登录Cookie，允许自动跳转UMC登录')
  }
  return false
}

// 清除退出登录Cookie
const clearLogoutCookie = () => {
  // 清除退出登录标识Cookie
  document.cookie = 'isLogout=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/'
  if (import.meta.env.VITE_APP_ENV === 'development') {
    console.log('已清除退出登录Cookie')
  }
}

// 直接UMC登录函数
const directUmcLogin = async () => {
  // 检查退出登录Cookie
  if (checkLogoutCookie()) {
    if (import.meta.env.VITE_APP_ENV === 'development') {
      console.log('检测到退出登录Cookie，跳过自动UMC登录，跳转到登录页')
    }
    // 如果有退出登录Cookie，跳转到登录页面让用户手动登录
    router.push('/login')
    return
  }

  // 清除退出登录Cookie（自动登录时清除）
  clearLogoutCookie()

  try {
    // 准备请求参数
    let ipParam: string | undefined

    if (import.meta.env.VITE_APP_ENV === 'development') {
      // 开发环境：传递当前页面的IP和端口
      ipParam = getCurrentHostInfo()
      console.log('开发环境，传递IP参数:', ipParam)
    }

    // 调用LoginRedirect接口获取登录地址
    const response = await LoginRedirect(ipParam)

    if (response.success && response.data) {
      // 有登录地址，直接跳转
      console.log('获取到UMC登录地址，直接跳转:', response.data)
      window.location.href = response.data
    } else {
      // 没有登录地址或接口失败，跳转到登录页面

      console.error('LoginRedirect接口失败:', response)
      console.log('跳转到登录页面让用户手动登录')
    }
  } catch (error) {
    // 接口调用异常，跳转到登录页面
    console.error('调用LoginRedirect接口异常:', error)
    console.log('跳转到登录页面让用户手动登录')
  }
}

// 路由守卫 - UMC登录核心逻辑
router.beforeEach((to, _from, next) => {
  const userData = localStorage.getItem('userData') || ''

  // 检查URL中是否包含UMC回调参数
  const currentUrl = window.location.href
  const urlParams = new URLSearchParams(window.location.search)
  const codeFromUrl = urlParams.get('code')
  const codeFromQuery = to.query.code as string

  // 检查是否是UMC回调URL
  const isUmcCallbackUrl = (currentUrl.includes('/UmcAuth/LoginCallback') && codeFromUrl) || (window.location.search.includes('code=') && codeFromUrl)
  const isUmcLogoutCallbackUrl = currentUrl.includes('/UmcAuth/LogoutCallback')

  // 检查是否是UMC回调路径
  const isUmcCallback = to.fullPath.includes('/UmcAuth/LoginCallback') || to.fullPath === '/loding'
  const isUmcLogoutCallback = to.fullPath.includes('/UmcAuth/LogoutCallback')
  // 获取code参数
  const code = codeFromUrl || codeFromQuery || ''

  // 调试信息
  if (import.meta.env.VITE_APP_ENV === 'development') {
    console.log('路由守卫:', {
      path: to.fullPath,
      code: code ? `${code.substring(0, 20)}...` : '',
      codeLength: code.length,
      isUmcCallbackUrl,
      isUmcLogoutCallbackUrl,
      isUmcCallback,
      isUmcLogoutCallback,
      userData: !!userData,
    })
  }

  // 如果是UMC退出登录回调，直接允许访问
  if (isUmcLogoutCallbackUrl || isUmcLogoutCallback) {
    if (import.meta.env.VITE_APP_ENV === 'development') {
      console.log('检测到UMC退出登录回调URL，允许访问')
    }
    next()
    return
  }

  // 如果检测到UMC登录回调参数，重定向到loading页面
  if ((isUmcCallbackUrl || code || isUmcCallback) && !userData) {
    if (import.meta.env.VITE_APP_ENV === 'development') {
      console.log('检测到UMC回调参数，处理登录回调')
    }

    // 存储所有回调参数到localStorage
    const fullCode = codeFromUrl || code
    if (fullCode) {
      localStorage.setItem('code', fullCode)
    }

    // 存储其他参数（进行URL解码处理）
    const scope = urlParams.get('scope') || ''
    const state = urlParams.get('state') || ''
    const session_state = urlParams.get('session_state') || ''
    const iss = urlParams.get('iss') || ''

    // URL解码并存储（特别处理加号）
    if (scope) localStorage.setItem('umc_scope', decodeURIComponent(scope.replace(/\+/g, ' ')))
    if (state) localStorage.setItem('umc_state', decodeURIComponent(state.replace(/\+/g, ' ')))
    if (session_state) localStorage.setItem('umc_session_state', decodeURIComponent(session_state.replace(/\+/g, ' ')))
    if (iss) localStorage.setItem('umc_iss', decodeURIComponent(iss.replace(/\+/g, ' ')))

    // 清理URL中的UmcAuth/LoginCallback路径
    if (window.location.pathname.includes('/UmcAuth/LoginCallback')) {
      const newUrl = `${window.location.origin}/${window.location.hash}`
      window.history.replaceState({}, document.title, newUrl)

      if (import.meta.env.VITE_APP_ENV === 'development') {
        console.log('路由守卫清理URL路径:', {
          原始URL: window.location.href,
          新URL: newUrl,
        })
      }
    }

    if (to.fullPath !== '/loding') {
      next({ path: '/loding' })
    } else {
      next()
    }
    return
  }

  // 正常的路由守卫逻辑
  if (userData) {
    // 检查Token是否过期（10分钟）
    try {
      const userDataObj = JSON.parse(userData)
      const loginTime = userDataObj.login_time || 0
      const currentTime = Math.floor(Date.now() / 1000)
      const expireTime = userDataObj.expire_time || 100 * 60000
      const isTokenExpired = currentTime - loginTime > expireTime

      if (isTokenExpired) {
        if (import.meta.env.VITE_APP_ENV === 'development') {
          console.log('Token已过期，清除用户数据并重新登录')
        }
        localStorage.removeItem('userData')
        // Token过期，重新登录
        if (to.fullPath === '/404') {
          next()
        } else {
          directUmcLogin()
        }
        return
      }

      // Token未过期，用户已登录
      if (to.fullPath === '/') {
        // 已登录用户访问登录页，重定向到主页面
        const menuList = userDataObj.permissions_infos
        if (menuList && menuList.length > 0) {
          const firstPage = menuList[0]?.children?.length > 0 ? menuList[0].children[0].path : menuList[0].path
          if (import.meta.env.VITE_APP_ENV === 'development') {
            console.log('已登录用户访问登录页，重定向到:', firstPage)
          }
          next({ path: firstPage })
          return
        }
        // 如果没有菜单权限，重定向到根路径
        next({ path: '/' })
        return
      }
      // 访问其他页面，更新最后使用时间并放行
      localStorage.setItem('lastUseTime', String(Math.floor(Date.now() / 1000)))
      next()
    } catch (error) {
      console.error('解析用户数据失败:', error)
      // 如果解析失败，清除无效数据
      localStorage.removeItem('userData')
      if (to.fullPath === '/404') {
        next()
      } else {
        directUmcLogin()
      }
    }
  } else if (to.fullPath === '/404') {
    // 未登录用户访问登录页或404页面，直接放行
    next()
  } else {
    // 直接调用UMC登录，不跳转到登录页
    directUmcLogin()
  }
})
app.mount('#app')

// 获取版本号
// const edition = () => {
//   const edition = localStorage.getItem('edition')
//   const VERSION = import.meta.env.VITE_APP_VERSION
//   if (edition) {
//     if (edition != VERSION) {
//       localStorage.clear()
//       return true
//     }
//   } else {
//     localStorage.setItem('edition', import.meta.env.VITE_APP_VERSION)
//   }
//   return false
// }
// 自动登录
if (window.performance.navigation.type == 0) {
  const autoLogin = localStorage.getItem('autoLogin')

  if (autoLogin !== 'true') {
    localStorage.removeItem('userData')
  }
}
// 超十五天未使用
if (Math.floor(Date.now() / 1000) - Number(localStorage.getItem('lastUseTime')) > 3600 * 24 * 15) {
  localStorage.removeItem('userData')
}
