<template>
  <div class="flex justify-between items-center mb-8">
    <div>
      <a-space>
        <div class="selected-box" v-if="checkItemsArr.length">
          <info-circle-filled class="text-[#409eff] mr-6" />
          <span class="mr-8">
            已选
            <span class="selected-text">{{ checkItemsArr.length }}</span>
            项数据
          </span>
          <span class="selected-text cursor-pointer" @click="clearCheckbox">清空</span>
        </div>
        <slot name="left-btn"></slot>
      </a-space>
    </div>
    <a-space>
      <slot name="right-btn"></slot>
      <LineHeightSetter v-model:type="lineHeightType" />
    </a-space>
  </div>
  <div class="tableBox">
    <!-- <vxe-toolbar class="toolbarBtn" ref="toolbarRef"></vxe-toolbar> -->
    <div
      class="box"
      v-show="tableVisble"
      :class="{
        stripe: !stripe,
      }"
    >
      <vxe-table
        :loading="tableLoading"
        :border="true"
        ref="tableRef"
        size="mini"
        :seq-config="{ startIndex: seqStartIndex }"
        @sort-change="sortChangeEvent"
        @resizable-change="({ $table, column }) => tableWColumnWidthChange(column, tableKey, pageType, $table)"
        :row-config="{
          keyField: keyField,
          isHover: true,
          height: lineHeightMap[lineHeightType],
        }"
        :custom-config="{ mode: 'popup' }"
        :data="tableData"
        :show-overflow="true"
        :show-header-overflow="true"
        :show-footer-overflow="true"
        height="100%"
        max-hegiht="100%"
        :column-config="{ resizable: true }"
        class="tableBoxwidth"
        :checkbox-config="{
          reserve: true,
          checkMethod: checkMethod,
        }"
        @checkbox-all="selectChangeEvent"
        @checkbox-change="selectChangeEvent"
        :stripe="stripe"
        :footer-data="footerData"
        :sort-config="{ remote: true }"
        v-bind="{
          showFooter: totalField?.length > 0,
          ...$attrs,
        }"
      >
        <slot name="column">
          <vxe-column v-if="isCheckbox" type="checkbox" field="checkbox" width="50" fixed="left"></vxe-column>
          <!-- 强制显示序号列，因为isIndex=true  -->
          <vxe-column v-if="isIndex" type="seq" title="序号" field="seq" :width="60" fixed="left" align="center"></vxe-column>
          <!-- 配置驱动的序号列（当没有isIndex时使用的） -->
          <vxe-column v-else-if="hasSeqColumn" type="seq" title="序号" field="seq" :width="seqColumnWidth" fixed="left" align="center"></vxe-column>
          <slot name="append"></slot>

          <template v-for="i in filteredTableKey" :key="i.key">
            <vxe-column
              :visible="i.is_show"
              :field="i.key"
              :title="i.name"
              :sortable="i.is_sort"
              :width="i.width"
              :fixed="i.freeze == 1 ? 'left' : i.freeze == 2 ? 'right' : ''"
              :formatter="i.formatter"
              v-bind="i"
            >
              <template #header>
                {{ i.name }}
                <a-tooltip v-if="i.tooltip">
                  <template #title>{{ i.tooltip }}</template>
                  <InfoCircleOutlined></InfoCircleOutlined>
                </a-tooltip>
              </template>

              <template v-if="$slots[i.key]" #default="attr">
                <slot :name="i.key" v-bind="attr" :item="i" />
              </template>
            </vxe-column>
          </template>
        </slot>
      </vxe-table>
    </div>
  </div>
  <div class="paginationBox">
    <div class="pagination">
      <a-pagination
        show-quick-jumper
        :total="total"
        show-size-changer
        v-model:current="page"
        v-model:page-size="pageSize"
        :page-size-options="pageSizeOptions"
        @showSizeChange="onShowSizeChange"
        @change="onChange"
        size="small"
      >
        <template #buildOptionText="props">
          <span>{{ props.value }}条/页</span>
        </template>
      </a-pagination>
    </div>
    <div class="totalBox">
      <div class="text">总数:</div>
      <div class="total">{{ total }}</div>
    </div>
  </div>

  <TableSetting v-model:columns="tableKey" v-model:visible="visible" v-model:form-arr="form" @save="handleSaveTableSetting" @reset="handleResetTableSetting" :page-type="pageType" @search="search" />
</template>
<script lang="ts" setup>
import { computed, PropType, nextTick } from 'vue'
import { checkFormParams, initTable, sumNum, tableWColumnWidthChange, setTableConfig } from '@/utils/index'
import { InfoCircleOutlined, InfoCircleFilled } from '@ant-design/icons-vue'
import { VxeTableInstance, VxeToolbarInstance } from 'vxe-table'
import TableSetting from '@/components/TableSetting.vue'
import LineHeightSetter from './LineHeightSetter.vue'

const tableLoading = ref(false)
const tableVisble = ref(false)
const toolbarRef = ref<VxeToolbarInstance>()
const tableRef = ref<VxeTableInstance>()
const pageSize = ref(20)
const total = ref(0)
const page = ref(1)
const tableData = ref()

const lineHeightType = ref(1)

const lineHeightMap = {
  1: 36,
  2: 52,
  3: 68,
}

const pageSizeOptions = ref(['20', '50', '100', '250'])

const form = defineModel('form', { default: [] })

const emits = defineEmits(['initFinish'])
const props = defineProps({
  title: String,
  getList: {
    type: Function,
    required: true,
  },
  formFormat: Function,
  dataFormat: Function,
  keyField: { default: 'id' },
  pageType: {
    type: Number,
    required: true,
  },
  isCheckbox: { default: false },
  isIndex: { default: false },
  stripe: Boolean,
  checkCb: Function,
  checkMethod: {
    type: Function as PropType<() => boolean>,
    default: undefined,
  },
  totalField: {
    type: Array as PropType<any[]> | null,
    default: null as any,
  },
  autoSearch: {
    type: Boolean,
    default: true,
  },
})

const tableKey = ref([] as any)

// 检查是否配置了序号列
const hasSeqColumn = computed(() => {
  return tableKey.value.some((item) => item.key === 'seq' && item.is_show)
})

// 获取序号列的宽度
const seqColumnWidth = computed(() => {
  const seqColumn = tableKey.value.find((item) => item.key === 'seq')
  return seqColumn?.width || 60
})

// 计算序号列的起始值
const seqStartIndex = computed(() => {
  return (page.value - 1) * pageSize.value
})

// 过滤掉序号列，因为我们使用 vxe-table 的内置序号列
const filteredTableKey = computed(() => {
  return tableKey.value.filter((item) => item.key !== 'seq')
})

const visibleData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {
    visibleData.isShow = false
  },
})
const ordersort = ref<string | boolean | null>(null)
const orderby = ref<string | null>(null)

onMounted(() => {
  const $table = tableRef.value
  const $toolbar = toolbarRef.value
  if ($table && $toolbar) {
    $table.connect($toolbar)
  }
  initTable(props.pageType, tableRef.value, tableKey, lineHeightType, false)
    .then((val) => {
      console.log(val, '=========val============')
      tableVisble.value = true
      emits('initFinish', val)
      if (props.autoSearch) tapQueryForm()
    })
    .catch((e) => {
      console.log(e, '=========e============')
    })
})

const search = () => {
  page.value = 1
  clearCheckbox()
  tapQueryForm()
}
// 查询
const tapQueryForm = () => {
  const obj: any = {}
  obj.page = page.value
  obj.pageSize = pageSize.value
  console.log(form.value, '=========s============')
  checkFormParams({ formArr: form.value, obj, callBack: getCustomerList }) // 校验完传入回调 -> 回调执行请求接口
}
// 分页数量的变化
const onShowSizeChange = () => {
  pageChange()
}
// 跳转第几页
const onChange = (i) => {
  page.value = i
  pageChange()
}

const pageChange = () => {
  const obj: any = {}
  obj.page = page.value
  obj.pageSize = pageSize.value
  checkFormParams({ formArr: form.value, obj, callBack: getCustomerList }) // 校验完传入回调 -> 回调执行请求接口
}
// table表头排序
const sortChangeEvent = ({ sortList }) => {
  console.log('sortList', sortList)
  orderby.value = null
  ordersort.value = null
  if (sortList.length != 0) {
    sortList.forEach((x) => {
      orderby.value = x.field
      // ordersort.value = x.order === 'asc'
      ordersort.value = x.order
    })
  }

  page.value = 1
  clearCheckbox()
  tapQueryForm()
}
// 查询表格数据
const getCustomerList = (obj) => {
  obj.sortField = orderby.value
  // obj.sort_asc = ordersort.value
  obj.sortType = ordersort.value
  tableLoading.value = true

  if (props.formFormat) obj = props.formFormat(obj)
  props
    .getList(obj)
    .then((res) => {
      // console.log('查询表格数据', res)

      const data = res.data.data || res.data.list
      tableLoading.value = false
      tableData.value = (props.dataFormat ? props.dataFormat(data) : data) || []
      total.value = res.data.total
      if (props.totalField?.length) statTotal(tableData.value)
    })
    .catch((error) => {
      // 处理API错误，停止加载状态并清空数据
      tableLoading.value = false
      tableData.value = []
      total.value = 0
      console.error('获取表格数据失败:', error)
    })
}
// 表格设置弹窗显示/隐藏
const visible = ref(false)
// 显示表格设置弹窗
const showTableSetting = () => {
  visible.value = true
}

const checkItemsArr = ref<any[]>([])
const selectChangeEvent = () => {
  const $table = tableRef.value
  // 当前页选中的数据
  const currentSelectedRows = $table?.getCheckboxRecords() || []
  // 其他页选中的数据
  const otherSelectedRows = $table?.getCheckboxReserveRecords() || []
  checkItemsArr.value = [...currentSelectedRows, ...otherSelectedRows]
  if (props.checkCb) {
    props.checkCb(checkItemsArr.value)
  }
  if (props.totalField?.length) statTotal(checkItemsArr.value)
}

const clearCheckbox = () => {
  tableRef.value?.clearCheckboxRow()
  tableRef.value?.clearCheckboxReserve()
  selectChangeEvent()
}

const footerData = ref([] as any)
const statTotal = (data: any[] = []) => {
  const $table = tableRef?.value
  if (!$table) return []

  const columns = $table.getColumns()
  const obj = {} as any
  data = data.length ? data : tableData.value || []
  columns.forEach((column, _columnIndex) => {
    if (_columnIndex === 0) {
      obj.checkbox = '合计'
      return
    }

    if (props.totalField?.includes(column.field)) {
      obj[column.field] = sumNum(data, column.field)
    }
  })
  footerData.value = [obj]
}
// 保存表格设置
const handleSaveTableSetting = async (arr) => {
  tableKey.value = []
  await setTableConfig(arr, props.pageType, lineHeightType.value)
  nextTick(() => {
    tableKey.value = arr
  })
  orderby.value = null
  ordersort.value = null
  search()
}

const handleResetTableSetting = async () => {
  const arr = await initTable(props.pageType, tableRef.value, tableKey, lineHeightType, true)
  orderby.value = null
  ordersort.value = null
  setTableConfig(arr, props.pageType, lineHeightType.value)
  search()
}

let isFirst = true
watch(
  () => lineHeightType.value,
  () => {
    if (isFirst) {
      isFirst = false
      return
    }
    setTableConfig(tableKey.value, props.pageType, lineHeightType.value)
  },
)

// 监听序号列相关变化，强制刷新表格列
watch(
  [() => props.isIndex, hasSeqColumn],
  () => {
    nextTick(() => {
      const $table = tableRef.value
      if ($table) {
        $table.refreshColumn()
      }
    })
  },
  { immediate: false },
)

defineExpose({
  refresh: tapQueryForm,
  tableData,
  checkItemsArr,
  tableRef,
  page,
  pageSize,
  ordersort,
  orderby,
  search,
  showTableSetting,
  tableKey,
  clearCheckbox,
  lineHeightType,
})
</script>
<style lang="scss" scoped>
.tableBox {
  position: relative;
  flex: 1;
  border-bottom: 1px solid #ddd;

  .tableBoxwidth {
    // width: fit-content;
    // max-width: 1662px;
  }

  .toolbarBtn {
    position: absolute;
    right: 0;
    bottom: 100%;
    padding: 0;
    padding-bottom: 0.6em;
    margin-block: -5px;
  }

  .box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    // overflow-y: scroll;
    .editbtn {
      color: #1890ff;
      cursor: pointer;
    }

    .movesea {
      margin-left: 20px;
      color: #1890ff;
      cursor: pointer;
    }
  }
}

.paginationBox {
  display: flex;
  align-items: center;
  margin-top: 0.83rem;

  .totalBox {
    display: flex;
    align-items: flex-end;
    margin-left: 20px;
    color: #000;

    .text {
      margin-right: 8px;
      font-size: 14px;
    }

    .total {
      font-size: 16px;
      color: #409eff;
    }
  }
}

.selected-box {
  display: flex;
  align-items: center;
  padding: 5px 12px;
  color: #3d3d3d;
  background-color: #f4f7fe;
  border: 1px solid #409eff;
  border-radius: 4px;

  .selected-text {
    color: #409eff;
  }
}

:deep(.stripe) {
  //  斑马纹
  tr:nth-of-type(even) {
    background-color: #f8f8f8;
  }
}

:deep(.vxe-table--body-wrapper.fixed-left--wrapper) {
  padding-bottom: 16px;
  overflow: hidden !important;
}

:deep(.ant-pagination-options .ant-select) {
  width: 82px;
}

// 调整“跳至”输入框大小deep(.ant-pagination-options-quick-jumper input) {
:deep(.ant-pagination-options-quick-jumper input) {
  margin-bottom: 3px !important;
}
</style>
