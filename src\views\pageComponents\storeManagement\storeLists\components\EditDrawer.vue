<template>
  <a-drawer
    :footer="drawerVisible ? undefined : false"
    v-model:open="drawerVisible"
    width="45vw"
    :title="formType == 'add' ? '新建店铺' : '编辑店铺'"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
    @afterOpenChange="formRef.clearValidate()"
    @close="onClose"
  >
    <div class="detailBox">
      <LoadingOutlined v-show="loading" class="loadingIcon" />
      <a-form ref="formRef" :model="editForm" :rules="rules">
        <div class="drawer-title">基础信息</div>
        <a-row>
          <a-col :span="12">
            <a-form-item label="店铺名称" name="shop_name">
              <a-input v-model:value="editForm.shop_name" placeholder="请输入店铺名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="公司" name="sub_company_id">
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                v-model:value="editForm.sub_company_id"
                placeholder="请选择所属公司"
                @change="changeCompany"
                :filter-option="(input, option) => filterOption(input, option)"
              >
                <a-select-option v-for="item in companyList" :key="item.company_id" :value="item.company_id" :label="item.company_name">{{ item.company_name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="部门" name="department_id">
              <a-tree-select
                show-search
                class="short"
                v-model:value="editForm.department_id"
                allow-clear
                :tree-data="departmentList"
                :fieldNames="{
                  children: 'childrenList',
                  label: 'departmentname',
                  value: 'departmentid',
                }"
                :maxTagCount="1"
                :listHeight="400"
                :dropdownMatchSelectWidth="250"
                placeholder="请选择所在部门"
                @click="onClickDepartment"
                @change="changeDepartment"
                treeNodeFilterProp="departmentname"
                :filter-option="(input, option) => filterOption(input, option)"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="负责人" name="owner_id">
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                v-model:value="editForm.owner_id"
                placeholder="请选择负责人"
                @click="onClickPrincipal"
                :filter-option="(input, option) => filterOption(input, option)"
              >
                <a-select-option v-for="item in userList" :key="item.id" :value="item.id" :label="item.label">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="平台(大类)" name="shop_platform_id">
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                v-model:value="editForm.shop_platform_id"
                placeholder="请选择平台大类"
                @change="onChangeLargeCategory"
                :filter-option="(input, option) => filterOption(input, option)"
              >
                <a-select-option v-for="item in categoryList" :key="item.id" :value="item.id" :label="item.name">{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="平台(小类)" name="shop_platform_subtype_id">
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                v-model:value="editForm.shop_platform_subtype_id"
                placeholder="请选择平台小类"
                @change="onChangeSubCategory"
                @click="onClickSubCategory"
                :filter-option="(input, option) => filterOption(input, option)"
              >
                <a-select-option v-for="item in subCategoryList" :key="item.id" :value="item.id" :label="item.name">{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="店铺类型" name="shop_type">
              <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" show-search v-model:value="editForm.shop_type" placeholder="请选择店铺类型" disabled>
                <a-select-option v-for="item in shopTypeList" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="shop_country_id">
              <template #label>
                <span>
                  国家/地区
                  <a-tooltip title="指店铺注册关联的主体归属的国家/地区">
                    <InfoCircleFilled style="font-size: 14px; color: #333" />
                  </a-tooltip>
                </span>
              </template>
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                v-model:value="editForm.shop_country_id"
                placeholder="请选择国家/地区"
                :filter-option="(input, option) => filterOption(input, option)"
              >
                <a-select-option v-for="item in countryList" :key="item.id" :value="item.id" :label="item.name">{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item name="shop_site_id">
              <template #label>
                <span>
                  站点
                  <a-tooltip title="指店铺实际运营的国家/地区">
                    <InfoCircleFilled style="font-size: 14px; color: #333" />
                  </a-tooltip>
                </span>
              </template>
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                v-model:value="editForm.shop_site_id"
                placeholder="请选择站点"
                :filter-option="(input, option) => filterOption(input, option)"
              >
                <a-select-option v-for="item in countryList" :key="item.id" :value="item.id" :label="item.name">{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="店铺状态" name="shop_status">
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                v-model:value="editForm.shop_status"
                placeholder="请选择店铺状态"
                :filter-option="(input, option) => filterOption(input, option)"
              >
                <a-select-option v-for="item in statusList" :key="item.value" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="邮箱" name="shop_email_id">
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                v-model:value="editForm.shop_email_id"
                placeholder="请选择邮箱"
                :filter-option="(input, option) => filterOption(input, option)"
                @search="handleSearch($event, 'email')"
              >
                <a-select-option v-for="item in emailList" :key="item.id" :value="item.id" :label="item.email">{{ item.email }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="手机号" name="shop_phone_id">
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                :filter-option="(input, option) => filterOption(input, option)"
                v-model:value="editForm.shop_phone_id"
                placeholder="请选择手机号"
                @search="handleSearch($event, 'phone')"
              >
                <a-select-option v-for="item in phoneList" :key="item.id" :value="item.id" :label="item.phone_number">{{ item.phone_number }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="店铺保证金" name="shop_deposit_amount">
              <a-input-number v-model:value="editForm.shop_deposit_amount" placeholder="请输入店铺保证金" :min="0" :max="999999" :precision="2" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="营业执照" name="business_license_id">
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                :filter-option="(input, option) => filterOption(input, option)"
                v-model:value="editForm.business_license_id"
                placeholder="请选择执照"
                @click="onClickLicense"
                @change="changeLicense"
                @search="handleSearch($event, 'license')"
              >
                <a-select-option v-for="item in licenseList" :key="item.id" :value="item.id" :disabled="!item.can_reg" :label="item.name">{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="法人姓名" name="legal_rep_id">
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                :filter-option="(input, option) => filterOption(input, option)"
                v-model:value="editForm.legal_rep_id"
                placeholder="请选择法人"
                @change="changeLegalPerson"
              >
                <a-select-option v-for="item in legalPersonList" :key="item.value" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="收款资金平台" name="payee_account_type">
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                :filter-option="(input, option) => filterOption(input, option)"
                v-model:value="editForm.payee_account_type"
                placeholder="请选择收款资金平台"
                @change="onChangeAccountType(1)"
              >
                <a-select-option v-for="item in fundingPlatList" :key="item.value" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="收款账户" name="payee_account_id">
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                :filter-option="(input, option) => filterOption(input, option)"
                v-model:value="editForm.payee_account_id"
                placeholder="请选择收款账户"
                @change="formRef.validateFields(['payee_account_id'])"
                @click="onClickPayee"
              >
                <a-select-option v-for="item in payeeAccountList" :key="item.value" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="收款账户名称" name="payee_account_id" :rules="[{ required: true, message: '请选择收款账户名称' }]">
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                :filter-option="(input, option) => filterOption(input, option)"
                v-model:value="editForm.payee_account_id"
                placeholder="请输入收款账户名称"
                @change="formRef.validateFields(['payee_account_id'])"
                @click="onClickPayee"
              >
                <a-select-option v-for="item in payeeAccountNameList" :key="item.value" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="付款资金平台" name="payer_account_type">
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                :filter-option="(input, option) => filterOption(input, option)"
                v-model:value="editForm.payer_account_type"
                placeholder="请选择付款资金平台"
                @change="onChangeAccountType(2)"
              >
                <a-select-option v-for="item in fundingPlatList" :key="item.value" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="付款账号" name="payer_account_id">
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                :filter-option="(input, option) => filterOption(input, option)"
                v-model:value="editForm.payer_account_id"
                placeholder="请选择付款账号"
                @click="onClickPayer"
              >
                <a-select-option v-for="item in payAccountList" :key="item.value" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="付款账户名称" name="payer_account_id">
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                :filter-option="(input, option) => filterOption(input, option)"
                v-model:value="editForm.payer_account_id"
                placeholder="请输入付款账户名称"
                @click="onClickPayer"
              >
                <a-select-option v-for="item in payAccountNameList" :key="item.value" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="创建人" name="applicant_username">
              <a-input v-model:value="editForm.creator" disabled />
            </a-form-item>
          </a-col>
        </a-row>
        <div class="drawer-title">账号信息</div>
        <a-row>
          <a-col :span="24">
            <a-space>
              <a-form-item label="总部财务账号" name="hq_finance_account">
                <a-input v-model:value="editForm.hq_finance_account" placeholder="请输入账号" class="w250" />
              </a-form-item>
              <a-form-item name="hq_finance_password">
                <a-input-password v-model:value="editForm.hq_finance_password" placeholder="请输入密码" class="w250" v-model:visible="pwdShow1" />
              </a-form-item>
            </a-space>
            <a-space>
              <a-form-item label="分部财务账号" name="sub_finance_account">
                <a-input v-model:value="editForm.sub_finance_account" placeholder="请输入账号" class="w250" />
              </a-form-item>
              <a-form-item name="sub_finance_password">
                <a-input-password v-model:value="editForm.sub_finance_password" placeholder="请输入密码" class="w250" v-model:visible="pwdShow2" />
              </a-form-item>
            </a-space>
            <a-space>
              <a-form-item label="运营账号" name="ops_account">
                <a-input v-model:value="editForm.ops_account" placeholder="请输入账号" class="w250" />
              </a-form-item>
              <a-form-item name="ops_password">
                <a-input-password v-model:value="editForm.ops_password" placeholder="请输入密码" class="w250" v-model:visible="pwdShow3" />
              </a-form-item>
            </a-space>
          </a-col>
        </a-row>
        <div class="drawer-title">外部店铺</div>
        <a-row>
          <a-col :span="24">
            <a-form-item label="聚水潭店铺">
              <a-space wrap>
                <a-select
                  :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                  show-search
                  :filter-option="(input, option) => filterOption(input, option)"
                  v-for="(_shopId, index) in editForm.jst_shop_list"
                  :key="index"
                  v-model:value="editForm.jst_shop_list[index]"
                  placeholder="请选择店铺"
                  class="w250"
                >
                  <a-select-option
                    v-for="item in jstShopList"
                    :key="item.shop_id"
                    :value="item.shop_id"
                    :label="item.shop_name"
                    :disabled="editForm.jst_shop_list.filter((n) => n == item.shop_id).length > 0"
                  >
                    <a-tooltip>
                      <template #title>{{ item.shop_name }}</template>
                      {{ item.shop_name }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
                <a-button v-if="editForm.jst_shop_list?.length > 1" class="icon-btn" shape="circle" :icon="h(MinusOutlined)" @click="onDeleteShop('jst')" />
                <a-button class="icon-btn" shape="circle" :icon="h(PlusOutlined)" @click="onAddshop('jst')" />
              </a-space>
            </a-form-item>
            <a-form-item label="马帮店铺">
              <a-space wrap>
                <a-select
                  :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                  show-search
                  :filter-option="(input, option) => filterOption(input, option)"
                  v-for="(_shopId, index) in editForm.mabang_shop_list"
                  :key="index"
                  v-model:value="editForm.mabang_shop_list[index]"
                  placeholder="请选择店铺"
                  class="w250"
                >
                  <a-select-option
                    v-for="item in mbShopList"
                    :key="item.shop_id"
                    :value="item.shop_id"
                    :label="item.shop_name"
                    :disabled="editForm.mabang_shop_list.filter((n) => n == item.shop_id).length > 0"
                  >
                    <a-tooltip>
                      <template #title>{{ item.shop_name }}</template>
                      {{ item.shop_name }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
                <a-button v-if="editForm.mabang_shop_list?.length > 1" class="icon-btn" shape="circle" :icon="h(MinusOutlined)" @click="onDeleteShop('mb')" />
                <a-button class="icon-btn" shape="circle" :icon="h(PlusOutlined)" @click="onAddshop('mb')" />
              </a-space>
            </a-form-item>
            <a-form-item label="自研ERP">
              <a-space wrap>
                <a-select
                  :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                  show-search
                  :filter-option="(input, option) => filterOption(input, option)"
                  v-for="(_shopId, index) in editForm.erp_shop_list"
                  :key="index"
                  v-model:value="editForm.erp_shop_list[index]"
                  placeholder="请选择店铺"
                  class="w250"
                >
                  <a-select-option
                    v-for="item in erpShopList"
                    :key="item.shop_id"
                    :value="item.shop_id"
                    :label="item.shop_name"
                    :disabled="editForm.erp_shop_list.filter((n) => n == item.shop_id).length > 0"
                  >
                    <a-tooltip>
                      <template #title>{{ item.shop_name }}</template>
                      {{ item.shop_name }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
                <a-button v-if="editForm.erp_shop_list?.length > 1" class="icon-btn" shape="circle" :icon="h(MinusOutlined)" @click="onDeleteShop('erp')" />
                <a-button class="icon-btn" shape="circle" :icon="h(PlusOutlined)" @click="onAddshop('erp')" />
              </a-space>
            </a-form-item>
          </a-col>
        </a-row>
        <div class="drawer-title">紫鸟设备绑定</div>
        <a-row>
          <a-col :span="24">
            <a-form-item label="企业" name="purple_bird_company_id">
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                :filter-option="(input, option) => filterOption(input, option)"
                v-model:value="editForm.purple_bird_company_id"
                placeholder="紫鸟企业"
                class="w250"
                @change="changePurpleBirdCompany"
              >
                <a-select-option v-for="item in purpleBirdCompanyList" :key="item.id" :value="item.id" :label="item.enterprise">
                  <a-tooltip>
                    <template #title>{{ item.enterprise }}</template>
                    {{ item.enterprise }}
                  </a-tooltip>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="设备名称" name="purple_bird_device_id">
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                show-search
                :filter-option="(input, option) => filterOption(input, option)"
                v-model:value="editForm.purple_bird_device_id"
                placeholder="紫鸟设备"
                class="w250"
                @click="onClickDevice"
              >
                <a-select-option v-for="item in deviceList" :key="item.id" :value="item.id" :label="item.device_name">{{ item.device_name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <template #footer>
      <a-button style="margin-right: 0.8333rem" type="primary" @click="onSubmit">确认</a-button>
      <a-button @click="onClose">取消</a-button>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import {
  Details,
  UpdateShop,
  AddShop,
  GetLicenseOptions,
  GetUserOptions,
  GetCategoryOptions,
  GetSubCategoryOptions,
  GetCountryOptions,
  GetEmailOptions,
  GetPhoneOptions,
  GetBiShopOptions,
  GetLegalPersonOptions,
  GetFundingPlatOptions,
  GetFinancialAccountOptions,
} from '@/servers/StoreLists'
import { GetMainAccountList, GetDeviceList } from '@/servers/PurpleBird'
import { GetDepartmentTreeList, GetCompanyList } from '@/servers/UserManager'
import { LoadingOutlined, MinusOutlined, PlusOutlined, InfoCircleFilled } from '@ant-design/icons-vue'
import { validateStr, filterOption } from '@/utils/index'
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import { h } from 'vue'

import { GetEnum } from '@/servers/Common'

const emit = defineEmits(['updateStatus', 'success'])
const drawerVisible = ref(false)
const loading = ref(false)
const formRef = ref()
const formType = ref('edit')
const oldStatus = ref(null)
const editForm = ref<any>({
  id: null,
  name: '',
  password: '',
  jst_shop_list: [null],
  mabang_shop_list: [null],
  erp_shop_list: [null],
  shop_country_id: null,
  shop_site_id: null,
  shop_status: null,
  shop_email_id: null,
  purple_bird_company_id: null,
  purple_bird_device_id: null,
  shop_platform_id: null,
  shop_platform_subtype_id: null,
})
const companyList = ref<any>([])
const departmentList = ref<any>([])
const userList = ref<any>([])
const categoryList = ref<any>([])
const subCategoryList = ref<any>([])
const shopTypeList = ref<any>([])
const countryList = ref<any>([])
const licenseList = ref<any>([])
const statusList = ref<any>([])
const emailList = ref<any>([])
const phoneList = ref<any>([])
const fundingPlatList = ref<any>([]) // 收款\付款资金平台
const legalPersonList = ref<any>([]) // 法人
const payeeAccountList = ref<any>([]) // 收款账号
const payeeAccountNameList = ref<any>([]) // 收款账号名称
const payAccountList = ref<any>([]) // 付款账号
const payAccountNameList = ref<any>([]) // 付款账号名称

const jstShopList = ref<any>([])
const mbShopList = ref<any>([])
const erpShopList = ref<any>([])
const purpleBirdCompanyList = ref<any>([])
const deviceList = ref<any>([])

const pwdShow1 = ref(false)
const pwdShow2 = ref(false)
const pwdShow3 = ref(false)

const inputValidator = (stringLen = 50) => {
  return {
    validator: (_rule, value) => validateStr(_rule, value, stringLen),
    message: `输入内容不可超过${stringLen}字符`,
  }
}

const rules: Record<string, Rule[]> = {
  shop_name: [{ required: true, trigger: ['change', 'blur'] }, inputValidator(100)],
  payee_account: [{ required: true, trigger: ['change', 'blur'] }, inputValidator()],
  payee_name: [{ required: true, trigger: ['change', 'blur'] }, inputValidator()],
  payer_account: [{ required: true, trigger: ['change', 'blur'] }, inputValidator()],
  payer_name: [{ required: true, trigger: ['change', 'blur'] }, inputValidator()],
  hq_finance_account: [{ required: true, trigger: ['change', 'blur'] }, inputValidator()],
  hq_finance_password: [{ required: true, trigger: ['change', 'blur'], message: '请输入总部财务密码' }, inputValidator()],
  sub_finance_account: [{ required: true, trigger: ['change', 'blur'] }, inputValidator()],
  sub_finance_password: [{ required: true, trigger: ['change', 'blur'], message: '请输入分部财务密码' }, inputValidator()],
  ops_account: [{ required: true, trigger: ['change', 'blur'] }, inputValidator()],
  ops_password: [{ required: true, trigger: ['change', 'blur'], message: '请输入运营密码' }, inputValidator()],
  sub_company_id: [{ required: true, trigger: ['change', 'blur'], message: '请选择公司' }],
  department_id: [{ required: true, trigger: ['change', 'blur'], message: '请选择部门' }],
  owner_id: [{ required: true, trigger: ['change', 'blur'], message: '请选择负责人' }],
  shop_platform_id: [{ required: true, trigger: ['change', 'blur'], message: '请选择平台大类' }],
  shop_platform_subtype_id: [{ required: true, trigger: ['change', 'blur'], message: '请选择平台小类' }],
  shop_type: [{ required: true, trigger: ['change', 'blur'], message: '请选择店铺类型' }],
  shop_status: [{ required: true, trigger: ['change', 'blur'], message: '请选择店铺状态' }],
  shop_deposit_amount: [{ required: true, trigger: ['change', 'blur'], message: '请输入店铺保证金' }],
  payee_account_type: [{ required: true, trigger: ['change', 'blur'], message: '请选择收款账户类型' }],
  payee_account_id: [{ required: true, trigger: ['change', 'blur'], message: '请选择收款账户' }],
}

const onSubmit = async () => {
  try {
    await formRef.value.validateFields()
    switch (formType.value) {
      case 'add':
        addSubmit()
        break
      case 'edit':
        updateSubmit()
        break
      default:
        break
    }
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
const handleSubmitData = (data) => {
  const obj = JSON.parse(JSON.stringify(data))
  obj.jst_shop_list = obj.jst_shop_list.filter((item) => item)
  obj.mabang_shop_list = obj.mabang_shop_list.filter((item) => item)
  obj.erp_shop_list = obj.erp_shop_list.filter((item) => item)
  Object.keys(obj).forEach((key) => {
    if (obj[key] === null) {
      delete obj[key]
    }
  })
  return obj
}
// 新增店铺账号
const addSubmit = () => {
  const obj = JSON.parse(JSON.stringify(handleSubmitData(editForm.value)))
  AddShop(obj).then(() => {
    message.success('新增成功')
    onClose()
    emit('success')
  })
}
// 编辑店铺账号
const updateSubmit = () => {
  const obj = JSON.parse(JSON.stringify(handleSubmitData(editForm.value)))
  obj.shop_id = obj.id
  obj.payer_account_id = obj.payer_account_id || 0
  UpdateShop(obj).then(() => {
    message.success('修改成功')
    onClose()
    emit('success')
  })
}
const onClose = () => {
  formRef.value.resetFields()
  drawerVisible.value = false
  pwdShow1.value = false
  pwdShow2.value = false
  pwdShow3.value = false
  editForm.value.jst_shop_list = [null]
  editForm.value.mabang_shop_list = [null]
  editForm.value.erp_shop_list = [null]
}
const handleFormData = (data) => {
  editForm.value.creator = `${data.applicant_username}/${data.applicant_name}`
  if (!editForm.value.jst_shop_list.length) {
    editForm.value.jst_shop_list.push(null)
  }
  if (!editForm.value.mabang_shop_list.length) {
    editForm.value.mabang_shop_list.push(null)
  }
  if (!editForm.value.erp_shop_list.length) {
    editForm.value.erp_shop_list.push(null)
  }
  editForm.value.jst_shop_list = data.jst_shop_list.map((item) => item?.shop_id)
  editForm.value.mabang_shop_list = data.mabang_shop_list.map((item) => item?.shop_id)
  editForm.value.erp_shop_list = data.erp_shop_list.map((item) => item?.shop_id)
  editForm.value.purple_bird_company_id = editForm.value.purple_bird_company_id || null
  editForm.value.purple_bird_device_id = editForm.value.purple_bird_device_id || null
  editForm.value.shop_country_id = editForm.value.shop_country_id || null
  editForm.value.shop_site_id = editForm.value.shop_site_id || null
  editForm.value.payer_account_id = editForm.value.payer_account_id || null
  editForm.value.business_license_id = editForm.value.business_license_id || null
  editForm.value.legal_rep_id = editForm.value.legal_rep_id || null
  editForm.value.shop_email_id = editForm.value.shop_email_id || null
  editForm.value.shop_phone_id = editForm.value.shop_phone_id || null
  editForm.value.payer_account_type = editForm.value.payer_account_type || null
}
const open = async (id, type) => {
  drawerVisible.value = true
  formType.value = type
  if (id) {
    loading.value = true
    editForm.value = {}
    const res = await Details({ shop_id: id, show_pwd: 1 })
    editForm.value = res.data
    editForm.value.name = res.data.device_name
    oldStatus.value = res.data.status
    loading.value = false
    getDepartmentTreeList()
    getUserList().then((list: any) => {
      if (!list.find((x) => x.id == editForm.value.owner_id)) {
        userList.value.push({
          id: editForm.value.owner_id,
          label: `${editForm.value.user_name}/${editForm.value.name}`,
        })
      }
    })
    getOptions(true)
    handleFormData(res.data)
  } else {
    getOptions(null)
    const userData = JSON.parse(localStorage.getItem('userData') as any)
    editForm.value.creator = `${userData?.user_name}/${userData?.real_name}`
  }
}

const onClickDepartment = () => {
  if (!editForm.value.sub_company_id) {
    message.info('请先选择所属公司')
  }
}
// 获取公司树状下拉框
const getCompanyList = () => {
  GetCompanyList().then((res) => {
    res.data.forEach((x) => {
      x.label = x.company_name
      x.value = x.company_id
    })
    companyList.value = res.data
  })
}

// 获取部门树状下拉框(内部)
const getDepartmentTreeList = () => {
  if (!editForm.value.sub_company_id) {
    return
  }
  GetDepartmentTreeList({ subcompanyid1: editForm.value.sub_company_id }).then((res) => {
    departmentList.value = res.data
  })
}

// 获取用户下拉列表
const getUserList = () => {
  return new Promise((resolve, reject) => {
    GetUserOptions({ dept_id: editForm.value.department_id })
      .then((res) => {
        userList.value = res.data.map((x: any) => {
          x.label = `${x.user_name}/${x.real_name}`
          x.value = x.user_id
          return x
        })
        resolve(userList.value)
      })
      .catch((error) => {
        reject(error)
      })
  })
}

const onClickPrincipal = () => {
  if (!editForm.value.department_id) {
    message.info('请先选择所在部门')
  }
}

const changeCompany = () => {
  getDepartmentTreeList()
  editForm.value.department_id = null
  editForm.value.owner_id = null
  userList.value = []
}

const changeDepartment = async () => {
  if (!editForm.value.department_id) {
    return
  }
  editForm.value.owner_id = null
  getUserList()
}

const onChangeLargeCategory = (val) => {
  editForm.value.shop_platform_subtype_id = null
  editForm.value.shop_type = null

  GetSubCategoryOptions({ categories: val, status: 1 }).then((res) => {
    subCategoryList.value = res.data
  })
}

const onClickSubCategory = () => {
  if (!editForm.value.shop_platform_id) {
    message.info('请先选择平台大类')
  }
}

const onClickLicense = () => {
  if (!editForm.value.shop_platform_subtype_id) {
    message.info('请先选择平台小类')
  }
}

type licenseType = {
  id: number
  name: string
  can_reg: number
  can_reg_times: number
  legal_person: string
  legal_person_id: string
  company_id: number
  attachment: string
}
const currentLicense = ref<licenseType>()
const changeLicense = (val) => {
  for (const i in licenseList.value) {
    if (licenseList.value[i].id == val) {
      currentLicense.value = licenseList.value[i]
      console.log('currentLicense.value', currentLicense.value)
      editForm.value.legal_rep_id = licenseList.value[i].legal_person_id
      formRef.value.validateFields(['legal_rep_name'])
      break
    }
  }
}

const onChangeSubCategory = (val) => {
  editForm.value.shop_type = null
  const shop_type = subCategoryList.value.find((x) => x.id == val).shop_type
  for (const i in shopTypeList.value) {
    if (shopTypeList.value[i].value == shop_type) {
      editForm.value.shop_type = shopTypeList.value[i].value
      formRef.value.validateFields(['shop_type'])
      break
    }
  }
  editForm.value.shop_deposit_amount = subCategoryList.value.filter((x) => x.id == val)[0]?.margin || 0
  formRef.value.validateFields(['shop_deposit_amount'])

  GetLicenseOptions({ platform_subtype: editForm.value.shop_platform_subtype_id, status: 1 }).then((res) => {
    licenseList.value = res.data
  })
}

const onAddshop = (type) => {
  switch (type) {
    case 'jst':
      editForm.value.jst_shop_list.push(null)
      break
    case 'mb':
      editForm.value.mabang_shop_list.push(null)
      break
    case 'erp':
      editForm.value.erp_shop_list.push(null)
      break
    default:
      break
  }
}

const onDeleteShop = (type) => {
  switch (type) {
    case 'jst':
      editForm.value.jst_shop_list.pop()
      break
    case 'mb':
      editForm.value.mabang_shop_list.pop()
      break
    case 'erp':
      editForm.value.erp_shop_list.pop()
      break
    default:
      break
  }
}

const onClickDevice = () => {
  if (!editForm.value.purple_bird_company_id) {
    message.info('请先选择紫鸟企业')
  }
}

const getDeviceList = () => {
  GetDeviceList({ purple_bird_primary_account_id: editForm.value.purple_bird_company_id, page: 1, pageSize: 10000 }).then((res) => {
    deviceList.value = res.data.list
  })
}
const changePurpleBirdCompany = () => {
  editForm.value.purple_bird_device_id = null
  getDeviceList()
}

let timeout: any
const postFuncByName = (value: string, key: string) => {
  const obj = {
    phone: GetPhoneOptions({ page: 1, pageSize: 250, phone_number: `${value}`, status: 1 }).then((res) => {
      phoneList.value = res.data.list
    }),
    license: GetLicenseOptions({ platform_subtype: editForm.value.shop_platform_subtype_id, name: `${value}`, status: 1 }).then((res) => {
      licenseList.value = res.data
    }),
    email: GetEmailOptions({ page: 1, pageSize: 250, email: `${value}`, status: 1 }).then((res) => {
      emailList.value = res.data.list
    }),
  }
  return obj[key]
}

const handleSearch = (value: string, key: string) => {
  if (timeout) {
    clearTimeout(timeout)
    timeout = null
  }
  function fake() {
    postFuncByName(value, key)
  }
  timeout = setTimeout(fake, 300)
}

const changeLegalPerson = (val) => {
  if (val != currentLicense.value?.legal_person_id) {
    editForm.value.business_license_id = null
  }
}

const onClickPayee = () => {
  if (!editForm.value.payee_account_type) {
    message.info('请先选择收款资金平台')
  }
}

const onClickPayer = () => {
  if (!editForm.value.payer_account_type) {
    message.info('请先选择付款资金平台')
  }
}

const onChangeAccountType = (type) => {
  // 收款 (1)、 付款(2)资金平台
  getAccountList(type)
  if (type == 1) {
    editForm.value.payee_account_id = null
  } else {
    editForm.value.payer_account_id = null
  }
}

const getAccountList = (type, isInit) => {
  if (type == 1) {
    GetFinancialAccountOptions({ status: 1, usage_type: 1, funding_platform_id: editForm.value.payee_account_type }).then((res) => {
      payeeAccountList.value = res.data.map((n) => {
        return {
          value: n.id,
          label: n.account,
        }
      })
      payeeAccountNameList.value = res.data.map((n) => {
        return {
          value: n.id,
          label: n.account_name,
        }
      })
      if (isInit) {
        if (!payeeAccountList.value.find((n) => n.id == editForm.value.payee_account_id)) {
          payeeAccountList.value.push({
            value: editForm.value.payee_account_id,
            label: editForm.value.payee_account,
          })

          payeeAccountNameList.value.push({
            value: editForm.value.payee_account_id,
            label: editForm.value.payee_account_name,
          })
        }
      }
    })
  } else {
    GetFinancialAccountOptions({ status: 1, usage_type: 2, funding_platform_id: editForm.value.payer_account_type }).then((res) => {
      payAccountList.value = res.data.map((n) => {
        return {
          value: n.id,
          label: n.account,
        }
      })
      payAccountNameList.value = res.data.map((n) => {
        return {
          value: n.id,
          label: n.account_name,
        }
      })
      if (isInit) {
        if (!payAccountList.value.find((n) => n.id == editForm.value.payer_account_id)) {
          payAccountList.value.push({
            value: editForm.value.payer_account_id,
            label: editForm.value.payer_account,
          })

          payAccountNameList.value.push({
            value: editForm.value.payer_account_id,
            label: editForm.value.payer_account_name,
          })
        }
      }
    })
  }
}

const getOptions = (isTypeEdit) => {
  getCompanyList()
  GetMainAccountList({ page: 1, pageSize: 10000 }).then((res) => {
    purpleBirdCompanyList.value = res.data.list
  })
  GetCategoryOptions({ status: 1 }).then((res) => {
    categoryList.value = res.data
    if (isTypeEdit && editForm.value.shop_platform_id && !categoryList.value.find((n) => n.id == editForm.value.shop_platform_id)) {
      categoryList.value.push({
        id: editForm.value.shop_platform_id,
        name: editForm.value.shop_platform,
      })
    }
  })
  GetCountryOptions({ status: 1 }).then((res) => {
    countryList.value = res.data
    if (isTypeEdit && editForm.value.shop_country_id && !countryList.value.find((n) => n.id == editForm.value.shop_country_id)) {
      countryList.value.push({
        id: editForm.value.shop_country_id,
        name: editForm.value.shop_country,
      })
    }
  })
  GetEmailOptions({ page: 1, pageSize: 250, status: 1 }).then((res) => {
    emailList.value = res.data.list
    if (isTypeEdit && editForm.value.shop_email_id && !emailList.value.find((n) => n.id == editForm.value.shop_email_id)) {
      emailList.value.push({
        id: editForm.value.shop_email_id,
        email: editForm.value.shop_email,
      })
    }
  })
  GetPhoneOptions({ page: 1, pageSize: 250, status: 1 }).then((res) => {
    phoneList.value = res.data.list
    if (isTypeEdit && editForm.value.shop_phone_id && !phoneList.value.find((n) => n.id == editForm.value.shop_phone_id)) {
      phoneList.value.push({
        id: editForm.value.shop_phone_id,
        phone_number: editForm.value.shop_phone,
      })
    }
  })
  GetLegalPersonOptions({ status: 1 }).then((res) => {
    legalPersonList.value = res.data.map((n) => {
      return {
        value: n.id,
        label: n.name,
      }
    })
    if (isTypeEdit && editForm.value.legal_rep_id && !legalPersonList.value.find((n) => n.id == editForm.value.legal_rep_id)) {
      legalPersonList.value.push({
        value: editForm.value.legal_rep_id,
        label: editForm.value.legal_rep_name,
      })
    }
  })
  GetFundingPlatOptions({ status: 1 }).then((res) => {
    fundingPlatList.value = res.data.map((n) => {
      return {
        value: n.id,
        label: n.name,
      }
    })
    if (isTypeEdit) {
      if (editForm.value.payee_account_type && !fundingPlatList.value.find((n) => n.id == editForm.value.payee_account_type)) {
        fundingPlatList.value.push({
          value: editForm.value.payee_account_type,
          label: editForm.value.payee_account_type_string,
        })
      }
      if (editForm.value.payer_account_type && !fundingPlatList.value.find((n) => n.id == editForm.value.payer_account_type)) {
        fundingPlatList.value.push({
          value: editForm.value.payer_account_type,
          label: editForm.value.payer_account_type_string,
        })
      }
    }
  })
  if (isTypeEdit) {
    GetLicenseOptions({ platform_subtype: editForm.value.shop_platform_subtype_id, status: 1 }).then((res) => {
      licenseList.value = res.data
      if (editForm.value.business_license_id && !licenseList.value.find((n) => n.id == editForm.value.business_license_id)) {
        licenseList.value.push({
          id: editForm.value.business_license_id,
          name: editForm.value.business_license,
        })
      }
    })
    GetSubCategoryOptions({ categories: editForm.value.shop_platform_id, status: 1 }).then((res) => {
      subCategoryList.value = res.data
      if (editForm.value.shop_platform_subtype_id && !subCategoryList.value.find((n) => n.id == editForm.value.shop_platform_subtype_id)) {
        subCategoryList.value.push({
          id: editForm.value.shop_platform_subtype_id,
          name: editForm.value.shop_platform_subtype,
        })
      }
    })
    getDeviceList()
    getAccountList(1)
    if (editForm.value.payer_account_type) {
      getAccountList(2)
    }
  }

  GetBiShopOptions({ shop_type: '聚水潭', is_self_shop: '内部' }).then((res) => {
    jstShopList.value = res.data
  })
  GetBiShopOptions({ shop_type: '马帮', is_self_shop: '内部' }).then((res) => {
    mbShopList.value = res.data
  })
  GetBiShopOptions({ shop_type: '自研ERP', is_self_shop: '内部' }).then((res) => {
    erpShopList.value = res.data
  })

  GetEnum().then((res) => {
    shopTypeList.value = res.data.shop.type
    statusList.value = res.data.shop.status
  })
}
onMounted(async () => {})
// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 110px;
    min-width: 110px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.detailTitle {
  padding-left: 12px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #000;
}

.w350 {
  width: 350px;
}

.w250 {
  width: 250px;
}

.w150 {
  width: 150px;
}

.w200 {
  width: 200px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: #00000080;
  white-space: nowrap;
}

.detailValueDescription {
  font-size: 12px;
  color: #00000080;
}

.detailBox {
  padding-top: 12px;

  .loadingIcon {
    font-size: 30px;
    color: #1890ff;
  }
}

.ant-input-number {
  width: 100%;
}

.icon-btn {
  scale: 0.8;
}
</style>
