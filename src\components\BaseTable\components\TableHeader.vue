<template>
  <div class="m-block-8px flex items-center justify-between table-header">
    <div>
      <a-space>
        <div class="selected-box" v-if="checkItemsArr.length">
          <info-circle-filled class="mr-6px text-[#409eff]" />
          <span class="mr-8px">
            已选
            <span class="selected-text">{{ checkItemsArr.length }}</span>
            项数据
          </span>
          <span class="selected-text cursor-pointer" @click="handleClear">清空</span>
        </div>
        <slot name="left-btn"></slot>
      </a-space>
    </div>
    <a-space :size="8">
      <slot name="right-btn"></slot>
      <table-height-setting v-model:type="type" />
    </a-space>
  </div>
</template>

<script setup lang="ts">
import { InfoCircleFilled } from '@ant-design/icons-vue'
import TableHeightSetting from './TableHeightSetting.vue'

const type = defineModel('type', { default: 1 })

defineProps<{
  checkItemsArr: any[]
}>()

const emit = defineEmits(['clear'])

const handleClear = () => {
  emit('clear')
}
</script>

<style scoped lang="scss">
.selected-box {
  display: flex;
  align-items: center;
  padding: 5px 12px;
  color: #3d3d3d;
  background-color: #f4f7fe;
  border: 1px solid #409eff;
  border-radius: 4px;

  .selected-text {
    color: #409eff;
  }
}

.table-header {
  :deep(.ant-btn-default) {
    color: #333 !important;
    border-color: #d9d9d9 !important;
  }
}
</style>
