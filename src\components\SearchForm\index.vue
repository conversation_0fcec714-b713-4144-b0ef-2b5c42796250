<template>
  <div class="gap-8px flex flex-col mb-[20px]">
    <div class="b-[1px] b-[#EAECEE] rounded-4px b-solid b-b-none relative bg-[#fafafa]">
      <div class="p-[8px] pb-[16px]" v-show="show">
        <FormQuick :is-quicks="isQuicks" v-model:quicks="quicks" @search="emtis('search')" />
        <div class="gap-4px mb-[8px] flex flex-wrap items-center">
          <FormComponent v-model:form-arr="formArr" />
          <a-space class="search-btn-box" :size="4">
            <a-button type="primary" @click="handleQuery">
              <span>查询</span>
            </a-button>
            <a-button @click="handleReset">
              <span>重置</span>
            </a-button>
            <a-button :icon="h(SettingOutlined)" @click="emtis('setting')">自定义设置</a-button>
            <SearchFormQuick :page-type="pageType" v-model:form-arr="formArr" @search="handleQuery" />
          </a-space>
        </div>
      </div>
      <SearchFormUnfold v-model:show="show" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { h } from 'vue'
import { SettingOutlined } from '@ant-design/icons-vue'
import SearchFormUnfold from './components/SearchFormUnfold.vue'
import FormComponent from './components/FormComponent.vue'
import FormQuick from './components/FormQuick.vue'
import SearchFormQuick from './components/SearchFormQuick.vue'
import { FormItem } from './type'

const emtis = defineEmits<{
  (e: 'search'): void
  (e: 'setting'): void
}>()

const props = defineProps<{
  clearCb?: () => void
  pageType: number
}>()

const show = ref(true)

const formArr = defineModel<FormItem[]>('form', { required: true })

const isQuicks = computed(() => formArr.value.some((v) => v.isQuicks && v.isShow))
const quicks = computed(() => formArr.value.filter((v) => v.isQuicks && v.isShow))

const handleQuery = () => {
  emtis('search')
}

const handleReset = () => {
  formArr.value.forEach((item) => {
    item.value = item.value instanceof Array ? [] : null
  })
  props?.clearCb && props.clearCb()
  handleQuery()
}

// 若没有值初始化复选筛选
const initScreening = () => {
  const path = useRoute().path
  const obj: any = localStorage.getItem('screeningObj') ? JSON.parse(localStorage.getItem('screeningObj') || '') : {}
  if (obj[path]) {
    const arr = [] as any
    formArr.value.forEach((y) => {
      obj[path].forEach((x) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value = formArr.value.map((v: any) => ({ ...v, isShow: v.isShow !== false }))
  }
}

onMounted(() => {
  initScreening()
})
</script>

<style scoped lang="scss"></style>
