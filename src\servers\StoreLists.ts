// 店铺管理
import { request } from './request'

// 获取店铺列表
export const GetShoppingList = (data) => {
  return request({ url: '/api/ShopManager/GetList', data })
}
// 查看总部账号
export const HqFinanceAccount = (data) => {
  return request({ url: '/api/ShopManager/HqFinanceAccount', data })
}
// 查看分部账号
export const SubFinanceAccount = (data) => {
  return request({ url: '/api/ShopManager/SubFinanceAccount', data })
}
// 查看运营账号
export const OpsAccount = (data) => {
  return request({ url: '/api/ShopManager/OpsAccount', data })
}
// 查看日志
export const LogList = (data) => {
  return request({ url: '/api/ShopManager/Log', data })
}
// 查看详情
export const Details = (data) => {
  return request({ url: '/api/ShopManager/Detail', data })
}
// 新建店铺
export const AddShop = (data) => {
  return request({ url: '/api/ShopManager/Add', data })
}
// 编辑店铺
export const UpdateShop = (data) => {
  return request({ url: '/api/ShopManager/Edit', data })
}

// 获取部门员工下拉列表
export const GetUserOptions = (data) => {
  return request({ url: '/api/ShopManager/GetUserListByDept', data })
}

// 获取平台大类下拉列表
export const GetCategoryOptions = (data) => {
  return request({ url: '/api/ShopManager/GetPlatformList', data })
}

// 获取平台小类下拉列表
export const GetSubCategoryOptions = (data) => {
  return request({ url: '/api/ShopManager/GetSubPlatformList', data })
}

// 获取营业执照下拉列表
export const GetLicenseOptions = (data) => {
  return request({ url: '/api/ShopManager/GetLicenseList', data })
}

// 获取国家下拉列表
export const GetCountryOptions = (data) => {
  return request({ url: '/api/ShopManager/GetCountryList', data })
}

// 获取邮箱下拉列表
export const GetEmailOptions = (data) => {
  return request({ url: '/api/ShopManager/GetEmailList', data })
}

// 获取手机号下拉列表
export const GetPhoneOptions = (data) => {
  return request({ url: '/api/ShopManager/GetPhoneList', data })
}

// 获取外部店铺下拉列表
export const GetBiShopOptions = (data) => {
  return request({ url: '/api/ShopManager/GetBiShopList', data })
}

// 获取法人下拉列表
export const GetLegalPersonOptions = (data) => {
  return request({ url: '/api/ShopManager/GetLegalPersonList', data })
}

// 资金平台列表
export const GetFundingPlatOptions = (data) => {
  return request({ url: '/api/ShopManager/GetFundingPlatList', data })
}

// 收付款账号列表
export const GetFinancialAccountOptions = (data) => {
  return request({ url: '/api/ShopManager/GetFinancialAccountList', data })
}

// 店铺注册申请
export const ApplyStore = (data) => {
  return request({ url: '/api/ShopManager/Reg', data })
}

// 店铺变更负责人
export const ChangeOwnerApply = (data) => {
  return request({ url: `/api/ShopManager/ChangeOwnerApply?shop_id=${data.shop_id}`, data })
}
