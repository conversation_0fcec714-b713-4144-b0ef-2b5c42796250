<template>
  <div class="main">
    <!-- 过滤器 -->
    <Form ref="formRef" v-model:form="formArr" @search="search" @setting="tableRef?.showTableSetting()" :page-type="PageType.SHOP_MANAGE" />
    <BaseTable ref="tableRef" :isCheckbox="true" :pageType="PageType.SHOP_MANAGE" :getList="getListFn" v-model:form="formArr" :auto-search="false">
      <template #left-btn>
        <a-button v-if="btnPermission[71006]" type="primary" @click="onOpenEdit(null, 'add')">新建店铺</a-button>
        <a-button v-if="btnPermission[71008]" type="primary" @click="onOpenRegist">申请注册</a-button>
      </template>
      <template #name="{ row }">
        <span>{{ row.username }}/{{ row.name }}</span>
      </template>
      <template #applicant_name="{ row }">
        <span>{{ row.applicant_username }}/{{ row.applicant_name }}</span>
      </template>
      <template #hq_finance_account="{ row }">
        <div v-if="!row?.hq_finance_accountAll" :class="['lookBtn', btnPermission[71002] ? 'blue' : 'grey']" @click="lookheadquarters(row)">点击查看</div>
        <div v-else>
          <div class="lookBox">
            <div class="lookText">账号：{{ row.hq_finance_accountAll.account }}</div>
            <div class="lookText">密码：{{ row.hq_finance_accountAll.password }}</div>
          </div>
        </div>
      </template>
      <template #sub_finance_account="{ row }">
        <div v-if="!row?.sub_finance_accountAll" :class="['lookBtn', btnPermission[71003] ? 'blue' : 'grey']" @click="lookbranch(row)">点击查看</div>
        <div v-else>
          <div class="lookBox">
            <div class="lookText">账号：{{ row.sub_finance_accountAll.account }}</div>
            <div class="lookText">密码：{{ row.sub_finance_accountAll.password }}</div>
          </div>
        </div>
      </template>
      <!-- ops_account -->
      <template #ops_account="{ row }">
        <div v-if="!row?.ops_accountAll" :class="['lookBtn', btnPermission[71004] ? 'blue' : 'grey']" @click="lookoperation(row)">点击查看</div>
        <div v-else>
          <div class="lookBox">
            <div class="lookText">账号：{{ row.ops_accountAll.account }}</div>
            <div class="lookText">密码：{{ row.ops_accountAll.password }}</div>
          </div>
        </div>
      </template>
      <template #fix_option="{ row }">
        <div class="btnBox">
          <a-tooltip :mouseEnterDelay="row.btns?.find((item) => item.btn_id == 71005)?.is_enable ? 10000 : 0.1">
            <template #title>
              <InfoCircleOutlined style="padding-right: 0.25rem" />
              <span>{{ row.btns?.find((item) => item.btn_id == 71005)?.tip }}</span>
            </template>
            <a-space>
              <a-button :disabled="row.shop_status == 4 || !row.btns?.find((item) => item.btn_id == 71005)?.is_enable" @click="detail(row)" class="btn">查看</a-button>
              <a-button :disabled="row.shop_status == 4 || !btnPermission[71007]" @click="onOpenEdit(row.id, 'edit')" class="btn">编辑</a-button>
              <a-button :disabled="row.shop_status == 4 || !btnPermission[71009] || row.shop_status !== 1" @click="onOpenEdit(row.id, 'change')" class="btn">变更</a-button>
            </a-space>
          </a-tooltip>
        </div>
      </template>
    </BaseTable>

    <!-- 确认弹窗 -->
    <a-modal :zIndex="10000" v-model:open="modalData.isShow" :title="modalData.title">
      <div class="modalContent">{{ modalData.content }}</div>
      <template #footer>
        <a-button v-if="modalData.isConfirmBtn" :danger="modalData.okType === 'danger'" type="primary" style="margin-right: 0.625rem" @click="modalData.okFn">{{ modalData.confirmBtnText }}</a-button>
        <a-button v-if="modalData.isCancelBtn" @click="modalData.isShow = false">取消</a-button>
      </template>
    </a-modal>
    <!-- 查看 -->
    <DetailDrawer ref="detailDrawerRef" />
    <EditDrawer ref="editDrawerRef" @success="search" />
    <RegistApplyDrawer ref="registDrawerRef" />
    <ChangeOwnerApplyDrawer ref="changeOwnerDrawerRef" />
  </div>
</template>
<script lang="ts" setup>
import Form from '@/components/Form.vue'
import { onMounted, ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import BaseTable from '@/components/BaseTable/index.vue'
import { GetShoppingList, HqFinanceAccount, SubFinanceAccount, OpsAccount, GetCategoryOptions, GetSubCategoryOptions, GetCountryOptions } from '@/servers/StoreLists'
import { PageType } from '@/common/enum'
import { GetEnum } from '@/servers/Common'
import DetailDrawer from './components/DetailDrawer.vue'
import EditDrawer from './components/EditDrawer.vue'
import RegistApplyDrawer from './components/RegistApplyDrawer.vue'
import ChangeOwnerApplyDrawer from './components/ChangeOwnerApplyDrawer.vue'

const tableRef = ref()
const formRef = ref()

const { btnPermission } = usePermission()

const getListFn = ref(GetShoppingList)

const formArr: any = ref([
  {
    label: '搜索店铺名称',
    value: null,
    type: 'input',
    key: 'shop_name',
  },
  {
    label: '平台（大类）',
    value: null,
    type: 'select_one',
    search: true,
    selectArr: [],
    key: 'shop_platform_id',
  },
  {
    label: '平台（小类）',
    value: null,
    type: 'select_one',
    search: true,
    selectArr: [],
    key: 'shop_platform_subtype_id',
  },
  {
    label: '店铺类型',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'shop_type_list',
    onChange: (val) => {
      console.log(val)
      if (val) {
        formArr.value.find((item) => item.key == 'shop_type_list').value = [val]
      } else {
        formArr.value.find((item) => item.key == 'shop_type_list').value = []
      }
    },
  },
  {
    label: '国家/地区',
    value: null,
    type: 'select_one',
    search: true,
    selectArr: [],
    key: 'shop_country_id',
  },
  {
    label: '搜索负责人名称/工号',
    value: null,
    type: 'input',
    key: 'owner_job_number_or_name',
    showLabelToolTip: true,
  },
  {
    label: '搜索创建人名称/工号',
    value: null,
    type: 'input',
    key: 'applicant_job_number_or_name',
    showLabelToolTip: true,
  },
  {
    label: '搜索邮箱',
    value: null,
    type: 'input',
    key: 'shop_email',
  },
  {
    label: '搜索手机号',
    value: null,
    type: 'input',
    key: 'shop_phone',
  },
  {
    label: '搜索营业执照',
    value: null,
    type: 'input',
    key: 'business_license',
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    selectArr: [],
    key: 'update_at',
    formKeys: ['create_start_at', 'create_end_at'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '店铺状态',
    value: null,
    key: 'shop_status_list',
    isShow: false,
    isQuicks: true,
    multiple: true,
    quickNotFull: true,
  },
])
// 确认框数据
const modalData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  okType: 'primary',
  title: '',
  content: '',
  okFn: () => {
    modalData.isShow = false
  },
})
// 查看
const detailDrawerRef = ref<any>(null)
const editDrawerRef = ref()
const registDrawerRef = ref()
const changeOwnerDrawerRef = ref()

const enumData = ref<any>({})
// const oldStatus = ref<any>(null)

const search = () => tableRef.value.search()

// 获取所有枚举选项
const getEnum = () => {
  GetEnum().then((res) => {
    enumData.value = res.data
    formArr.value.forEach((item) => {
      if (item.key == 'shop_status_list') {
        item.selectArr = enumData.value.shop.status
      }
      if (item.key == 'shop_type_list') {
        item.selectArr = enumData.value.shop.type
      }
    })
  })
}

onMounted(() => {
  getSelect()
  search()
  getEnum()
  initScreening()
})
onActivated(() => {})

// 查看总部账号
const lookheadquarters = (row) => {
  if (!btnPermission.value[71002]) {
    message.error('暂无查看权限，如需查看请联系管理员。')
    return
  }
  HqFinanceAccount({ shop_id: row.id }).then((res) => {
    console.log(res)
    row.hq_finance_accountAll = res.data
  })
}
// 查看分部
const lookbranch = (row) => {
  if (!btnPermission.value[71003]) {
    message.error('暂无查看权限，如需查看请联系管理员。')
    return
  }
  SubFinanceAccount({ shop_id: row.id }).then((res) => {
    row.sub_finance_accountAll = res.data
  })
}
// 查看运营
const lookoperation = (row) => {
  if (!btnPermission.value[71004]) {
    message.error('暂无查看权限，如需查看请联系管理员。')
    return
  }
  OpsAccount({ shop_id: row.id }).then((res) => {
    row.ops_accountAll = res.data
  })
}

const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.SHOP_MANAGE) {
    const arr: any[] = []
    obj.SHOP_MANAGE.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}

const onOpenEdit = (id, type) => {
  if (type == 'change') {
    changeOwnerDrawerRef.value?.open(id)
  } else {
    editDrawerRef.value?.open(id, type)
  }
}
// 详情
const detail = (item) => {
  detailDrawerRef.value.open(item)
}
// 店铺申请注册
const onOpenRegist = () => {
  registDrawerRef.value?.open()
}

// 获取下拉选项
const getSelect = () => {
  GetCategoryOptions(null).then((res) => {
    formArr.value.find((item) => item.key == 'shop_platform_id').selectArr = res.data.map((item) => {
      return {
        label: item.name,
        value: item.id,
      }
    })
  })
  GetSubCategoryOptions(null).then((res) => {
    formArr.value.find((item) => item.key == 'shop_platform_subtype_id').selectArr = res.data.map((item) => {
      return {
        label: item.name,
        value: item.id,
      }
    })
  })
  GetCountryOptions(null).then((res) => {
    formArr.value.find((item) => item.key == 'shop_country_id').selectArr = res.data.map((item) => {
      return {
        label: item.name,
        value: item.id,
      }
    })
  })
}
</script>

<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .breadcrumbBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 1.875rem;
    margin: 0.5rem 0;

    .title {
      font-size: 1rem;
      font-weight: bold;
      color: #000;
    }
  }

  .btnlist {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-top: 0.9375rem;

    .batchBtn {
      margin-left: 0.625rem;
    }
  }

  .formBox {
    .operatingAreaBox {
      display: flex;
      align-items: center;
      height: 2rem;

      .tag {
        padding: 0 0.625rem;
        cursor: pointer;
        user-select: none;
      }

      .btn {
        margin-right: 0.625rem;
      }
    }
  }


}

.modalContent {
  font-size: 0.875rem;
  word-break: break-word;
  white-space: pre-wrap;
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 7.5rem;
    min-width: 7.5rem;
    margin-right: 1.875rem;

    label {
      font-size: 0.9375rem;

      &::after {
        display: none !important;
      }
    }
  }
}

.lookBtn {
  cursor: pointer;
}

.blue {
  color: #1890ff;
}

.grey {
  color: #aaa;
}

.lookBox {
  display: flex;
}

.lookText {
  margin-right: 0.625rem;
}

.w240 {
  width: 15rem;
}

.description {
  padding-left: 1.25rem;
  font-size: 0.75rem;
  color: rgb(0 0 0 / 50%);
  white-space: nowrap;
}

.loadingIcon {
  font-size: 1.875rem;
  color: #1890ff;
}

.TagFilter {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-top: 0.5rem;

  .item {
    margin-right: 1.25rem;
  }
}
</style>
