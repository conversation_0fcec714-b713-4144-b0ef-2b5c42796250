<template>
  <a-drawer
    :footer="logVisble ? undefined : false"
    v-model:open="detailVisible"
    :width="appStore.isOpenLog ? '91vw' : '45vw'"
    title="查看小类"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
    :bodyStyle="{ padding: '0' }"
  >
    <template #extra>
      <a-button @click="changeLogVisible">日志</a-button>
    </template>
    <div class="detailAllBox">
      <LoadingOutlined v-show="detailloading" class="loadingIcon" />

      <a-form v-if="!detailloading && detail">
        <a-collapse v-model:activeKey="activeKey" :bordered="true" expandIconPosition="end" ghost>
          <template #expandIcon="{ isActive }">
            <DoubleRightOutlined :rotate="isActive ? 270 : 90" />
          </template>
          <a-collapse-panel key="1" header="平台小类" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="8">
                <p class="label">状态</p>
                <p class="value">{{ detail.status ? '启用' : '停用' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">小类名称</p>
                <p class="value">{{ detail.name }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">品牌授权</p>
                <p class="value">{{ detail.brand_authorization === 1 ? '是' : '否' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">店铺类型</p>
                <p class="value">{{ detail.store_type_name }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">证件类型</p>
                <p class="value">{{ detail.document_type_name }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">注册次数</p>
                <p class="value">{{ detail.registration_limit }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">保证金</p>
                <p class="value">{{ detail.is_margin ? '是' : '否' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">注册指定站点</p>
                <p class="value">{{ detail.register_designated_site_name }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8" v-if="detail.is_margin">
                <p class="label">保证金缴纳节点</p>
                <p class="value">{{ detail.margin_payment_node_name }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8" v-if="detail.is_margin">
                <p class="label">保证金金额</p>
                <p class="value">{{ detail.margin }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">二次缴费</p>
                <p class="value">{{ detail.second_payment ? '是' : '否' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8" v-if="detail.second_payment">
                <p class="label">缴费类型</p>
                <p class="value">{{ detail.payment_type == 1 ? '诚信通' : '--' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8" v-if="detail.second_payment">
                <p class="label">缴费金额</p>
                <p class="value">{{ detail.payment_amount }}</p>
              </a-col>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel key="2" header="其他" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="8">
                <p class="label">创建时间</p>
                <p class="value">
                  {{ detail?.create_at || '--' }}
                </p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">创建人</p>
                <p class="value">
                  <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
                    <template #title>
                      <div>用户名称：{{ detail?.create_user?.real_name }}</div>
                      <div v-show="detail?.create_user?.scope == 1">所属公司：{{ detail?.create_user?.sub_company_name ? detail?.create_user?.sub_company_name : '--' }}</div>
                      <div v-show="detail?.create_user?.scope != 1">所属客户：{{ detail?.create_user?.update_user_customer_name ? detail?.create_user?.update_user_customer_name : '--' }}</div>
                      <div>所在部门：{{ detail?.create_user?.department ? detail?.create_user?.department : '--' }}</div>
                      <div>
                        岗
                        <span style="visibility: hidden">占位</span>
                        位：{{ detail?.create_user?.job_name ? detail?.create_user?.job_name : '--' }}
                      </div>
                    </template>
                    <div style="display: flex; align-items: center">
                      <span class="detailValue">{{ detail?.create_user?.real_name ? detail?.create_user?.real_name : '--' }}</span>
                      <span v-if="detail?.create_user?.department || detail?.create_user?.job_name" class="detailValueDescription">
                        （
                        <span v-if="detail?.create_user?.job_name">{{ detail?.create_user?.job_name }}&nbsp;|&nbsp;</span>
                        <span v-if="detail?.create_user?.department">
                          {{ detail?.create_user?.department.length > 10 ? detail?.create_user?.department.slice(0, 10) + '...' : detail?.create_user?.department }}
                        </span>
                        ）
                      </span>
                    </div>
                  </a-tooltip>
                </p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">最后修改时间</p>
                <p class="value">{{ detail.update_at }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">最后修改人</p>
                <p class="value">
                  <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
                    <template #title>
                      <div>用户名称：{{ detail?.modified_user?.real_name }}</div>
                      <div v-show="detail?.modified_user?.scope == 1">所属公司：{{ detail?.modified_user?.sub_company_name ? detail?.modified_user?.sub_company_name : '--' }}</div>
                      <div v-show="detail?.modified_user?.scope != 1">所属客户：{{ detail?.modified_user?.update_user_customer_name ? detail?.modified_user?.update_user_customer_name : '--' }}</div>
                      <div>所在部门：{{ detail?.modified_user?.department ? detail?.modified_user?.department : '--' }}</div>
                      <div>
                        岗
                        <span style="visibility: hidden">占位</span>
                        位：{{ detail?.modified_user?.job_name ? detail?.modified_user?.job_name : '--' }}
                      </div>
                    </template>
                    <div style="display: flex; align-items: center">
                      <span class="detailValue">{{ detail?.modified_user?.real_name ? detail?.modified_user?.real_name : '--' }}</span>
                      <span v-if="detail?.modified_user?.department || detail?.modified_user?.job_name" class="detailValueDescription">
                        （
                        <span v-if="detail?.modified_user?.job_name">{{ detail?.modified_user?.job_name }}&nbsp;|&nbsp;</span>
                        <span v-if="detail?.modified_user?.department">
                          {{ detail?.modified_user?.department.length > 10 ? detail?.modified_user?.department.slice(0, 10) + '...' : detail?.modified_user?.department }}
                        </span>
                        ）
                      </span>
                    </div>
                  </a-tooltip>
                </p>
              </a-col>
            </a-row>
          </a-collapse-panel>
        </a-collapse>
      </a-form>
      <log-drawer ref="logDrawerRef" class="log" v-if="!detailloading && appStore.isOpenLog" />
    </div>
  </a-drawer>
  <!-- 日志 -->
</template>

<script lang="ts" setup>
import { LoadingOutlined, DoubleRightOutlined } from '@ant-design/icons-vue'

import { Details } from '@/servers/platformSubCateg'

import useAppStore from '@/store/modules/app'
import LogDrawer from './LogDrawer.vue'

const appStore = useAppStore()

const customStyle = 'overflow: hidden; border: .0625rem solid #eee; margin-bottom: 1.25rem;'
const activeKey = ref(['1', '2'])

const logDrawerRef = ref()
const detailVisible = ref(false)
const detailloading = ref(false)
const logVisble = ref(false)
const detail = ref<any>(null)
const open = (id, boolean) => {
  detail.value = null
  detailloading.value = true
  detailVisible.value = true
  logVisble.value = boolean
  Details({ id })
    .then((res) => {
      detail.value = res.data
      detailloading.value = false
      nextTick(() => {
        logDrawerRef.value?.open(detail.value)
      })
    })
    .catch(() => {
      detailloading.value = false
    })
}

const changeLogVisible = () => {
  appStore.changeLogOpenVisible(appStore.isOpenLog ? 0 : 1)
  if (appStore.isOpenLog) {
    nextTick(() => {
      logDrawerRef.value?.open(detail.value)
    })
  }
}

// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
:deep(.ant-collapse-header) {
  background-color: #f4f7fe;
}
</style>
