<template>
  <!-- 抽屉组件，从右侧滑出 -->
  <a-drawer v-model:open="visible" title="商品提审" class="submit-audit-drawer-container" placement="right" @cancel="handleCancel" :width="800" :destroy-on-close="true">
    <!-- 操作按钮（固定在抽屉右上角） -->
    <template #extra>
      <div class="header-actions">
        <a-button type="primary" :loading="submitLoading" class="mr-8px" @click="handleSubmit">提审</a-button>
        <a-button @click="handleSave" :loading="saveLoading">保存</a-button>
      </div>
    </template>

    <!-- 警告提示 - 紧靠标题下面，铺满 -->
    <div class="warning-notice">
      <ExclamationCircleOutlined class="text-orange-500 mr-8px" />
      <span>
        若需要提审，请确保已经
        <span class="text-red-500">与供应商协商并达成一致</span>
        ： “供货价” 、“采购计划” 。
      </span>
    </div>

    <div class="submit-audit-drawer">
      <!-- 表单内容 -->
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical" @finish="handleSubmit">
        <!-- 选品信息区域 -->
        <div class="drawer-title">选品信息</div>
        <a-form-item label="选品意见" name="selection_notes" required class="selection-notes-item">
          <a-textarea v-model:value="formData.selection_notes" placeholder="请填写选择商品的原因" :rows="3" :maxlength="300" show-count />
        </a-form-item>

        <!-- 采购计划区域 -->
        <div class="drawer-title">采购计划</div>
        <a-row :gutter="16" style="margin-top: 16px">
          <a-col :span="8">
            <a-form-item label="首批新品采购量" name="first_batch_quantity" required>
              <a-input v-model:value="formData.first_batch_quantity" placeholder="请输入首批新品采购量" addon-after="pcs" :maxlength="9" show-count @input="handleFirstBatchQuantityInput" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="shipping_fee" required>
              <template #label>
                <span>运费</span>
                <a-tooltip title="供应商发货需要我方支付的运费。若运费由供应商支付，此处金额为0。">
                  <QuestionCircleOutlined class="ml-4px text-gray-400" />
                </a-tooltip>
              </template>
              <!-- 运费改为数字输入组件 -->
              <a-input-number
                v-model:value="formData.shipping_fee"
                placeholder="请输入首批采购运费"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 100%"
                maxlength="12"
                max="*********.99"
                show-count
                addon-after="CNY"
                @change="handleShippingFeeChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="发货时间" name="shipment_time" required>
              <a-date-picker v-model:value="formData.shipment_time" placeholder="请输入发货时间" style="width: 100%" value-format="YYYY-MM-DD" @change="handleShipmentTimeChange" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16" style="margin-top: 16px">
          <a-col :span="8">
            <a-form-item label="预计交期" name="expected_delivery_date" required>
              <a-date-picker v-model:value="formData.expected_delivery_date" placeholder="请选择预计交期" style="width: 100%" value-format="YYYY-MM-DD" />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 采购价区域 -->
        <div class="drawer-title">采购价</div>
        <div class="price-display">
          <div class="price-item">
            <div class="price-label">采购单价(含税)(供应商报价)</div>
            <div class="price-value">¥ {{ productInfo.declared_purchase_tax_price || '0.00' }}</div>
          </div>
        </div>
        <a-row :gutter="16" style="margin-top: 16px">
          <a-col :span="8">
            <a-form-item name="agreed_purchase_tax_price" required>
              <template #label>
                <span>采购单价(含税)(双方协商后定价)</span>
                <a-tooltip title="跟供应商采购商品的含税单价。由采购员线下跟供应商议价，跟供应商协商达成一致的“含税采购单价”。">
                  <QuestionCircleOutlined class="ml-4px text-gray-400" />
                </a-tooltip>
              </template>
              <a-input-number
                v-model:value="formData.agreed_purchase_tax_price"
                placeholder="请输入采购单价(含税)"
                :min="0"
                :precision="2"
                :step="0.01"
                maxlength="12"
                max="*********.99"
                style="width: 100%"
                addon-after="CNY"
                @change="handlePurchasePriceInput"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 店铺供货价区域 -->
        <div class="drawer-title">店铺供货价</div>
        <a-form-item name="store_supply_price" required>
          <template #label>
            <span>店铺供货价</span>
            <a-tooltip title="店铺供货给选品网的商品单价。">
              <QuestionCircleOutlined class="ml-4px text-gray-400" />
            </a-tooltip>
          </template>
          <a-input-number
            v-model:value="formData.store_supply_price"
            placeholder="请填写店铺供货价"
            :min="0"
            :precision="2"
            maxlength="12"
            max="*********.99"
            :step="0.01"
            style="width: 200px"
            addon-after="CNY"
            @change="handleStoreSupplyPriceInput"
          />
        </a-form-item>
      </a-form>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { ExclamationCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue'
import { SubmitAudit, GetProduct } from '@/servers/SupplierProduceStock'
import type { FormInstance } from 'ant-design-vue'

// 表单数据类型定义
interface FormData {
  selection_notes: string
  first_batch_quantity: string | number | null
  shipping_fee: number | null // 运费改为数字类型
  shipment_time: string | null
  expected_delivery_date: string
  agreed_purchase_price: number | null
  agreed_purchase_tax_rate: number | null
  agreed_purchase_tax_price: number | null
  store_supply_price: number | null
}

// 定义事件
const emit = defineEmits<{
  success: []
}>()

// 响应式变量
const visible = ref(false)
const saveLoading = ref(false)
const submitLoading = ref(false)
const productId = ref<number>(0)
const formRef = ref<FormInstance>()
const productInfo = ref<any>({})

// 表单数据
const formData = reactive<FormData>({
  selection_notes: '',
  first_batch_quantity: null,
  shipping_fee: null, // 运费初始化为null
  shipment_time: '',
  expected_delivery_date: '',
  agreed_purchase_price: null,
  agreed_purchase_tax_rate: null,
  agreed_purchase_tax_price: null,
  store_supply_price: null,
})

// 自定义验证函数 - 预计交期必须大于发货时间
const validateDeliveryDate = (_rule: any, value: string) => {
  if (!value) {
    return Promise.resolve()
  }
  if (!formData.shipment_time) {
    return Promise.resolve()
  }

  const shipmentDate = new Date(formData.shipment_time)
  const deliveryDate = new Date(value)

  if (deliveryDate <= shipmentDate) {
    return Promise.reject(new Error('预计交期必须大于发货时间'))
  }

  return Promise.resolve()
}

// 表单验证规则
const rules = {
  selection_notes: [{ required: true, message: '请输入选品意见', trigger: 'blur' }],
  first_batch_quantity: [{ required: true, message: '请输入首批新品采购量', trigger: 'blur' }],
  shipping_fee: [{ required: true, message: '请输入运费', trigger: 'blur' }],
  shipment_time: [{ required: true, message: '请选择发货时间', trigger: 'change' }],
  expected_delivery_date: [
    { required: true, message: '请选择预计交期', trigger: 'change' },
    { validator: validateDeliveryDate, trigger: 'change' },
  ],
  agreed_purchase_price: [{ required: true, message: '请输入不含税单价', trigger: 'blur' }],
  agreed_purchase_tax_rate: [{ required: true, message: '请选择税率', trigger: 'change' }],
  agreed_purchase_tax_price: [{ required: true, message: '请输入含税单价', trigger: 'blur' }],
  store_supply_price: [{ required: true, message: '请输入店铺供货价', trigger: 'blur' }],
}

// 获取商品信息
const fetchProductInfo = async (id: number) => {
  try {
    const res = await GetProduct({ id })
    if (res.success) {
      productInfo.value = res.data
      fillFormWithExistingData(res.data)
    }
  } catch (error) {
    console.error('获取商品信息失败:', error)
  }
}

// 数据回显函数
const fillFormWithExistingData = (data: any) => {
  if (data.selection_notes) {
    formData.selection_notes = data.selection_notes
  }
  if (data.first_batch_quantity !== null && data.first_batch_quantity !== undefined) {
    formData.first_batch_quantity = data.first_batch_quantity
  }
  if (data.shipping_fee !== null && data.shipping_fee !== undefined) {
    formData.shipping_fee = data.shipping_fee // 数字类型直接赋值
  }
  if (data.shipment_time) {
    formData.shipment_time = data.shipment_time
  }
  if (data.expected_delivery_date) {
    formData.expected_delivery_date = data.expected_delivery_date
  }
  if (data.agreed_purchase_price !== null && data.agreed_purchase_price !== undefined) {
    formData.agreed_purchase_price = data.agreed_purchase_price
  }
  if (data.agreed_purchase_tax_rate !== null && data.agreed_purchase_tax_rate !== undefined) {
    formData.agreed_purchase_tax_rate = data.agreed_purchase_tax_rate
  }
  if (data.agreed_purchase_tax_price !== null && data.agreed_purchase_tax_price !== undefined) {
    formData.agreed_purchase_tax_price = data.agreed_purchase_tax_price
  }
  if (data.store_supply_price !== null && data.store_supply_price !== undefined) {
    formData.store_supply_price = data.store_supply_price
  }
}

// 显示抽屉
const showModal = async (id: number) => {
  productId.value = id
  resetForm()
  await fetchProductInfo(id)
  visible.value = true
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    selection_notes: '',
    first_batch_quantity: null,
    shipping_fee: null, // 重置为null
    shipment_time: '',
    expected_delivery_date: '',
    agreed_purchase_price: null,
    agreed_purchase_tax_rate: null,
    agreed_purchase_tax_price: null,
    store_supply_price: null,
  })
  productInfo.value = {}
  formRef.value?.resetFields()
}

// 关闭抽屉
const handleCancel = () => {
  if (hasFormDataChanged()) {
    Modal.confirm({
      title: '确认关闭',
      content: '您有未保存的内容，关闭后将丢失，是否继续？',
      okText: '确认关闭',
      cancelText: '取消',
      onOk: () => {
        visible.value = false
        resetForm()
      },
    })
  } else {
    visible.value = false
    resetForm()
  }
}

// 检查表单数据是否有变化
const hasFormDataChanged = (): boolean => {
  return Object.values(formData).some((value) => value !== null && value !== '')
}

// 根据含税单价和税率反推不含税单价
const calculateBasePrice = () => {
  if (formData.agreed_purchase_tax_price && formData.agreed_purchase_tax_rate !== null) {
    const taxRate = formData.agreed_purchase_tax_rate / 100
    formData.agreed_purchase_price = Number((formData.agreed_purchase_tax_price / (1 + taxRate)).toFixed(2))
  }
}

// 发货时间改变时，重新验证预计交期
const handleShipmentTimeChange = () => {
  if (formData.expected_delivery_date) {
    formRef.value?.validateFields(['expected_delivery_date'])
  }
}

// 处理首批新品采购量输入（只允许数字）
const handleFirstBatchQuantityInput = (value: string) => {
  const numericValue = value.replace(/[^\d]/g, '')
  formData.first_batch_quantity = numericValue
}

// 运费变更处理（数字组件不需要复杂的格式处理）
const handleShippingFeeChange = (value: number | null) => {
  // 数字组件已通过precision限制小数位数，这里只需简单处理
  formData.shipping_fee = value
}

// 处理采购单价输入限制（小数后2位）
const handlePurchasePriceInput = (value: number | null) => {
  if (value !== null && value !== undefined) {
    const rounded = Math.round(value * 100) / 100
    formData.agreed_purchase_tax_price = rounded
    calculateBasePrice()
  }
}

// 处理店铺供货价输入限制（小数后2位）
const handleStoreSupplyPriceInput = (value: number | null) => {
  if (value !== null && value !== undefined) {
    const rounded = Math.round(value * 100) / 100
    formData.store_supply_price = rounded
  }
}

// 保存表单（不提交审核）
const handleSave = async () => {
  try {
    saveLoading.value = true

    const params = {
      id: productId.value,
      selection_notes: formData.selection_notes,
      first_batch_quantity: Number(formData.first_batch_quantity),
      shipping_fee: formData.shipping_fee || 0, // 直接使用数字
      shipment_time: formData.shipment_time!,
      expected_delivery_date: formData.expected_delivery_date,
      agreed_purchase_price: formData.agreed_purchase_price!,
      agreed_purchase_tax_rate: formData.agreed_purchase_tax_rate!,
      agreed_purchase_tax_price: formData.agreed_purchase_tax_price!,
      store_supply_price: formData.store_supply_price!,
      is_audit: false,
    }

    await SubmitAudit(params)
    message.success('保存成功')
    visible.value = false
  } catch (error) {
    console.error('保存失败:', error)
    message.error('保存失败，请检查输入是否正确')
  } finally {
    saveLoading.value = false
  }
}

// 提交审核
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    submitLoading.value = true

    const params = {
      id: productId.value,
      selection_notes: formData.selection_notes,
      first_batch_quantity: Number(formData.first_batch_quantity),
      shipping_fee: formData.shipping_fee || 0, // 直接使用数字
      shipment_time: formData.shipment_time!,
      expected_delivery_date: formData.expected_delivery_date,
      agreed_purchase_price: formData.agreed_purchase_price!,
      agreed_purchase_tax_rate: formData.agreed_purchase_tax_rate!,
      agreed_purchase_tax_price: formData.agreed_purchase_tax_price!,
      store_supply_price: formData.store_supply_price!,
      is_audit: true,
    }

    await SubmitAudit(params)
    message.success('提交审核成功')
    visible.value = false
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    message.error('提交失败，请重试')
  } finally {
    submitLoading.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  showModal,
})
</script>
<style lang="scss">
.submit-audit-drawer-container {
  .ant-drawer-body {
    padding: 0 !important;
  }
}
</style>
<style scoped lang="scss">
.warning-notice {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
  color: #000;
  background-color: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 0;
}

.submit-audit-drawer {
  box-sizing: border-box;
  height: calc(100% - 50px);
  padding: 20px;
  overflow-y: auto;

  .section-title {
    padding-bottom: 8px;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    border-bottom: 1px solid #f0f0f0;
  }

  .price-notice {
    margin-top: 8px;
    font-size: 12px;
    line-height: 1.5;
  }

  .footer-actions {
    padding-top: 16px;
    margin-top: 24px;
    text-align: left;
    border-top: 1px solid #f0f0f0;
  }

  .text-orange-500 {
    color: #d46b08;
  }

  .text-red-500 {
    color: #ff4d4f;
  }

  .mb-16px {
    margin-bottom: 16px;
  }

  .mr-8px {
    margin-right: 8px;
  }

  .plan-item-label {
    margin-bottom: 4px;
    font-size: 14px;
    color: #666;
  }

  .plan-item-value {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }

  .price-display {
    padding: 12px 16px;
    margin-bottom: 16px;
    border-radius: 4px;
  }

  .price-item {
    .price-label {
      margin-bottom: 4px;
      font-size: 14px;
      color: #666;
    }

    .price-value {
      font-size: 16px;
      font-weight: 500;
      color: #1890ff;
    }
  }

  .header-actions {
    display: flex;
    gap: 8px;
  }

  .ml-4px {
    margin-left: 4px;
  }

  .text-gray-400 {
    color: #9ca3af;
  }
}

.selection-notes-item {
  margin-top: -8px !important;
  margin-bottom: 16px !important;

  :deep(.ant-form-item-label) {
    margin-bottom: 2px !important;
  }
}
</style>
