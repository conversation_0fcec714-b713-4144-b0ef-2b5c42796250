<template>
  <div>
    <div class="detailBox">
      <div class="drawer-title">日志</div>

      <LoadingOutlined v-show="logloading" class="loadingIcon" />
      <div v-if="!logloading && log.length != 0" class="contentBox">
        <div class="detail" v-for="(item, index) in log" :key="index">
          <div class="point">
            <div class="pointItem"></div>
          </div>
          <div class="detailTime">{{ item.op_at }}</div>
          <div class="detailContentBox">
            <div class="detailTitle">
              {{ item.user_name }}
              <span v-show="item.user_department" class="description">[{{ item.user_department }}]</span>
            </div>
            <div class="detailType">{{ item.op_type }}了 此记录</div>
            <div class="detailItem" style="display: flex" v-for="(detail, detailIndex) in item.edits" :key="detailIndex">
              <div style="white-space: nowrap">· {{ detail.name }}：</div>
              <div class="oldVal">{{ detail.old_val ? detail.old_val : '空' }}</div>
              <div class="to">></div>
              <div class="newVal">{{ detail.new_val ? detail.new_val : '空' }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="emptyText" v-show="!logloading && log.length === 0">该邮箱无操作日志</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { GetOpLogInfos } from '@/servers/EmailMana'
import { LoadingOutlined } from '@ant-design/icons-vue'

const logVisible = ref(false)
const logloading = ref(false)
const target = ref(null)
interface Log {
  user_name: string
  user_department: string
  op_type: string
  edits: any[]
  op_at: string
}
const log = ref<Log[]>([])
const open = (item) => {
  log.value = []
  target.value = item
  logVisible.value = true
  logloading.value = true
  GetOpLogInfos({ id: item.id })
    .then((res) => {
      log.value = res.data.reverse()
      logloading.value = false
    })
    .catch(() => {
      logloading.value = false
    })
}

defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
.detailBox {
  width: 100%;
  margin-left: 30px;

  .drawer-title {
    width: calc(100% - 60px);
  }
}

.contentBox {
  padding: 5px 0 5px 20px;
  margin-left: 20px;
  border-left: 1px solid #409eff;

  .detail {
    position: relative;
    margin-bottom: 12px;

    .point {
      position: absolute;
      top: 2px;
      left: -28px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 15px;
      height: 15px;
      background-color: rgb(255 255 255);
      border: 1px solid #409eff;
      border-radius: 50%;

      .pointItem {
        width: 9px;
        height: 9px;
        background-color: #409eff;
        border-radius: 50%;
      }
    }

    .detailContentBox {
      padding: 16px;
      background: #f7f8fa;
      border-radius: 4px;

      .detailTitle {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        color: rgb(0 0 0 / 80%);
      }

      .description {
        padding-left: 10px;
        font-size: 12px;
        color: #999;
      }

      .detailType {
        margin-bottom: 8px;
        color: #999;
      }

      .detailItem {
        line-height: 20px;
        color: rgb(0 0 0 / 80%);

        .oldVal {
          height: auto;
          color: #999;
          text-decoration: line-through;
          word-break: break-all;
        }

        .newVal {
          height: auto;
          color: #000;
          word-break: break-all;
        }

        .to {
          margin: 0 10px;
          color: #666;
        }
      }
    }

    .detailTime {
      margin-bottom: 12px;
      font-size: 12px;
      color: #999;
    }
  }
}

.emptyText {
  font-size: 12px;
  color: rgb(0 0 0 / 70%);
}
</style>
