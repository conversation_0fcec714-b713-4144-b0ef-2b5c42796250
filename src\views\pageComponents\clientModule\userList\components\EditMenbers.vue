<template>
  <a-drawer v-model:open="openDrawer" placement="right" :width="500" :maskClosable="false" title="编辑用户" @after-open-change="afterOpenChange" @close="closeDrawer">
    <a-form ref="formRef" :model="formState" :rules="rules" :label-col="{ style: { width: '100px' } }" :wrapper-col="{ span: 17 }">
      <!-- 基本信息 -->
      <a-form-item label="姓名" name="real_name">
        <a-input placeholder="请输入姓名" v-model:value="formState.real_name" :maxlength="50" :disabled="true" />
      </a-form-item>

      <a-form-item label="账号" name="uid">
        <a-input placeholder="请输入账号" v-model:value="formState.uid" disabled />
      </a-form-item>

      <a-form-item label="工号" name="job_id">
        <a-input placeholder="请输入工号" v-model:value="formState.job_id" :maxlength="showOaDepartment?.company_category_type == 1 ? 6 : 30" disabled />
      </a-form-item>

      <a-form-item label="所属部门" name="department_ids">
        <a-select
          showArrow
          mode="multiple"
          placeholder="请选择所属部门"
          v-model:value="formState.department_ids"
          :options="departmentOption"
          @deselect="handleDepartmentDeselect"
          :open="false"
          disabled
        >
          <template #tagRender="{ label, closable, onClose, value }">
            <a-tag class="dragTag" :closable="closable" style="display: flex; align-items: center; margin: 2px; font-size: 12px; cursor: pointer" :key="value" @close="onClose">
              <span>{{ label }}</span>
            </a-tag>
          </template>
        </a-select>
      </a-form-item>

      <a-form-item label="直接上级" name="leader_name">
        <a-select
          showArrow
          showSearch
          allowClear
          optionFilterProp="account_name"
          placeholder="请选择直接上级"
          v-model:value="formState.leader_name"
          :options="leaderOption"
          :field-names="{ label: 'account_name_lebel', value: 'id' }"
          disabled
        >
          <template #option="{ account_name, company_name }">
            <div class="select-option">
              <div>{{ account_name }}</div>
              <div class="text-12px text-#999">{{ company_name }}</div>
            </div>
          </template>
        </a-select>
      </a-form-item>
      <a-form-item label="系统角色">
        <a-select
          showArrow
          showSearch
          optionFilterProp="label"
          :placeholder="roleDisplayPlaceholder"
          :value="displayRoleValue"
          :options="roleOption"
          @change="handleRoleChange"
          :disabled="isRoleSelectDisabled"
          allowClear
        >
          <!-- 自定义显示选中的值 -->
          <template #suffixIcon v-if="isSuperAdmin">
            <span></span>
          </template>
          <template #empty>
            <div class="text-gray-500">暂无角色选项</div>
          </template>
        </a-select>
      </a-form-item>
    </a-form>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="gap-12px flex">
        <a-button :loading="submitLoading" type="primary" @click="handleSave">保存</a-button>
        <a-button :loading="submitLoading" @click="closeDrawer">取消</a-button>
      </div>
    </template>
  </a-drawer>
  <select-depart v-if="showSelectDepart" ref="selectDepartRef" @change="handleDepartmentChange" :tree-data="departmentTreeData" @close="showSelectDepart = false" />
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted } from 'vue'
import type { FormInstance } from 'ant-design-vue'
import { message } from 'ant-design-vue'
import { GetList as GetUserDetail, UpdateAccountRole, GetUserInfo } from '@/servers/UserManagerNew'
import { GetList as GetRoleList, GetRoleSelectOption } from '@/servers/RoleNew'
import { GetCompanyTree } from '@/servers/CompanyArchitecture'
import Sortable from 'sortablejs'
import { cloneDeep, createLoadingDebounce } from '@/utils'
import SelectDepart from './SelectDepart.vue'

const emit = defineEmits(['query', 'close'])
const openDrawer = ref(false)
const selectDepartRef = ref()
const submitLoading = ref(false)
const formRef = ref<FormInstance>()
// 表单数据
const formState = ref<any>({
  department: [],
  role_ids: '',
  role_names: [],
})
// 原始用户数据（用于判断是否为超级管理员）
const originalUserData = ref<any>({})
// 部门树数据
const departmentTreeData = ref<any[]>([])
// 企业下拉框
const enterpriseOption = ref<any[]>([])
// 部门下拉框
const departmentOption = ref<any[]>([])
// 岗位下拉框
// const jobOption = ref([])
// 直接上级下拉框
const leaderOption = ref([])
// 关联OA成员下拉框
const oaMemberOption = ref<any[]>([])
// 系统角色下拉框
const roleOption = ref<any[]>([])

const showSelectDepart = ref(false)
const isRoleSelectDisabled = ref(false)

// 计算属性：角色显示的占位符文本
const roleDisplayPlaceholder = computed(() => {
  // 检查原始用户数据中是否是超级管理员
  if (originalUserData.value.role_ids) {
    const roleIds = originalUserData.value.role_ids
      .toString()
      .split(',')
      .map((id: string) => id.trim())
    const isSuperAdmin = roleIds.some((id: string) => ['1', '2'].includes(id))

    if (isSuperAdmin) {
      return '超级管理员'
    }
  }

  return '请选择系统角色'
})

// 计算属性：显示的角色值
const displayRoleValue = computed(() => {
  // 如果是超级管理员或空字符串，返回空值
  if (isSuperAdmin.value || formState.value.role_ids === "") {
    return undefined
  }

  return formState.value.role_ids
})

// 计算属性：是否为超级管理员
const isSuperAdmin = computed(() => {
  const roleIds = originalUserData.value.role_ids
    ? originalUserData.value.role_ids.toString().split(',').map((id: string) => id.trim())
    : []
  return roleIds.some((id: string) => ['1', '2'].includes(id))
})

// 表单验证规则（响应式）
const rules = computed(() => ({
  real_name: [{ required: true, message: '请完善姓名信息' }],
  uid: [{ required: true, message: '请完善账号信息' }],
  department_ids: [{ required: true, message: '请完善部门信息' }],
}))

// 获取直接上级下拉框
const GetLeaderList = (id: any) => {
  // 临时使用用户列表接口获取同企业的用户作为上级选项
  return GetUserDetail({
    page: 1,
    pageSize: 100,
    company_id: id,
  }).then((res) => {
    leaderOption.value = res.data.list.map((item: any) => ({
      ...item,
      account_name_lebel: `${item.account_name}( ${item.account_id} )`,
    }))
  })
}

// 获取成员详情
const getMemberInfo = (id: string) => {
  return GetUserInfo({
    umcAccountId: id,
  }).then((res) => {
    const userData = res.data || {}
    console.log('EditMembers - 用户数据:', userData)

    // 处理角色信息 - 如果是超级管理员，设置为空值以便使用 placeholder 显示
    let currentRoleIds = ''
    let currentRoleNames = []

    if (userData.role_ids) {
      const roleIds = userData.role_ids
        .toString()
        .split(',')
        .map((id: string) => id.trim())
      const isSuperAdmin = roleIds.some((id: string) => ['1', '2'].includes(id))

      // 只有非超级管理员角色才设置到表单中，超级管理员通过 placeholder 显示
      if (!isSuperAdmin) {
        currentRoleIds = userData.role_ids
        if (userData.role_names && Array.isArray(userData.role_names) && userData.role_names.length > 0) {
          currentRoleNames = userData.role_names
        }
      }
    }

    formState.value = {
      ...userData,
      department: userData.department || [], // 确保是数组
      department_ids: userData.department ? userData.department.map((item: any) => item.id) : [],
      role_ids: currentRoleIds,
      role_names: currentRoleNames,
    }

    // 保存原始用户数据，用于计算属性判断是否为超级管理员
    originalUserData.value = {
      ...userData,
    }

    departmentOption.value = userData.department
      ? userData.department.map((item: any, index: any) => ({
          label: item.name || item.department_name,
          value: item.id,
          index,
        }))
      : []

    console.log('EditMembers - 设置的角色信息:', {
      role_ids: currentRoleIds,
      role_names: currentRoleNames,
    })
  })
}

// 获取角色下拉框 - 统一获取所有角色（排除超级管理员）
const GetRoleListData = () => {
  // 尝试使用专门的角色下拉框接口
  return GetRoleSelectOption({})
    .then((res) => {
      if (res.data && Array.isArray(res.data)) {
        const roles = res.data
          .filter((item: any) => item.status === 1) // 只显示启用状态的角色
          .filter((item: any) => !['1', '2'].includes(`${item.role_id || item.id}`)) // 排除超级管理员角色
          .map((item: any) => ({
            label: item.role_name || item.label,
            value: `${item.role_id || item.id}`,
          }))

        roleOption.value = roles
      } else {
        roleOption.value = []
      }
    })
    .catch(() => {
      // 如果专用接口失败，回退到列表接口
      return GetRoleList({ page: 1, pageSize: 500 })
        .then((res) => {
          if (res.data && res.data.list && Array.isArray(res.data.list)) {
            const roles = res.data.list
              .filter((item: any) => item.status === 1) // 只显示启用状态的角色
              .filter((item: any) => !['1', '2'].includes(`${item.id}`)) // 排除超级管理员角色
              .map((item: any) => ({
                label: item.role_name,
                value: `${item.id}`,
              }))

            roleOption.value = roles
          } else {
            roleOption.value = []
          }
        })
        .catch((listError) => {
          console.error('获取角色数据失败:', listError)
          message.error('获取角色数据失败，请检查网络连接或权限设置')
          roleOption.value = []
        })
    })
}

// 处理角色选择变化
const handleRoleChange = (val: any) => {
  console.log('角色选择已更新:', val)

  // 如果用户清空了选择，直接设置为空
  if (!val) {
    formState.value.role_ids = ''
    formState.value.role_names = []
    return
  }

  formState.value.role_ids = val
  // 找到对应的角色名称
  const selectedRole = roleOption.value.find((item) => item.value === val)
  if (selectedRole) {
    formState.value.role_names = [selectedRole.label]
  } else {
    formState.value.role_names = []
  }
  console.log('角色选择已更新:', val, formState.value.role_names)
}

// 获取岗位下拉框
const GetJobList = () => {
  return Promise.resolve()
}

const setPathName = (data: any, treeData: any) => {
  data.forEach((item: any) => {
    treeData.forEach((item1: any) => {
      if (item.id == item1.id) {
        item.company_name = item1.company_name
      }
      if (item1.childs && item1.childs.length > 0) {
        setPathName(data, item1.childs)
      }
    })
  })
}

// 获取部门下拉框
const GetDepartmentList = (companyId: any) => {
  return GetCompanyTree({}).then((res) => {
    // 查找指定企业下的所有部门数据
    const findCompanyTree = (nodes: any[], targetId: string): any[] => {
      for (const node of nodes) {
        if (node.id === targetId && node.type === 1) {
          // 找到目标企业，返回其子节点（部门数据）
          return node.childs || []
        }
        if (node.childs && node.childs.length > 0) {
          const result = findCompanyTree(node.childs, targetId)
          if (result.length > 0) {
            return result
          }
        }
      }
      return []
    }

    const companyTree = findCompanyTree(res.data || [], companyId)
    setPathName(formState.value.department, companyTree)
    departmentTreeData.value = companyTree // 保存树形数据
  })
}

// 获取企业下拉框
const GetEnterpriseList = () => {
  return GetCompanyTree({}).then((res) => {
    // 递归查找所有企业节点（type=1）
    const filterCompanies = (nodes: any[]): any[] => {
      const companies: any[] = []

      const traverse = (nodeList: any[]) => {
        nodeList.forEach((node) => {
          if (node.type === 1) {
            companies.push({
              label: node.department_name || node.company_name,
              value: node.id,
              company_category_type: node.company_category_type || 0,
            })
          }
          if (node.childs && node.childs.length > 0) {
            traverse(node.childs)
          }
        })
      }

      traverse(nodes)
      return companies
    }

    enterpriseOption.value = filterCompanies(res.data || [])
  })
}

// 打开抽屉
const showDrawer = async (row: any) => {
  openDrawer.value = true

  try {
    // 首先获取用户信息
    await getMemberInfo(row.id)

    // 并行获取其他必要数据
    await Promise.all([
      GetRoleListData(), // 获取角色下拉框数据（包含超级管理员，用于显示）
      GetJobList(),
      GetEnterpriseList(),
      GetLeaderList(formState.value.company_id),
      GetDepartmentList(formState.value.company_id),
    ])

    // 如果是西月集团，获取OA成员选项
    if (showOaDepartment.value?.company_category_type == 1) {
      await getOaMemberOption()
    }

    nextTick(() => {
      onDrop()
    })
  } catch (error) {
    console.error('加载用户编辑数据失败:', error)
    message.error('加载数据失败，请重试')
  }
}
// 当企业为西月时才显示oa关联
const showOaDepartment = computed(() => {
  const current = enterpriseOption.value.find((item: any) => item.value === formState.value.company_id)
  return current
})
// 获取关联OA成员
const getOaMemberOption = async () => {
  oaMemberOption.value = []
}

// 处理部门选择变化
const handleDepartmentChange = async (departments: any[]) => {
  showSelectDepart.value = false
  formState.value.department = departments
  formState.value.department_ids = formState.value.department.map((item: any) => item.id)
  departmentOption.value = departments.map((item, index: any) => ({
    label: item.department_name,
    value: item.id,
    key: item.id,
    index,
  }))
}
// 处理部门标签删除
const handleDepartmentDeselect = (value: string) => {
  formState.value.department = formState.value.department.filter((item: any) => item.id !== value)
  departmentOption.value = formState.value.department.map((item: any, index: any) => ({
    label: item.department_name,
    value: item.id,
    index,
    key: item.id,
  }))
}
// 关闭抽屉
const closeDrawer = () => {
  openDrawer.value = false
  emit('close')
}

// 保存编辑用户核心逻辑
const saveEditUserCore = async () => {
  formRef.value
    ?.validate()
    .then(() => {
      submitLoading.value = true
      console.log('form', formState.value)

      // 处理角色ID：如果是超级管理员且当前角色为空，则使用原始角色ID
      let roleIdsToSave = formState.value.role_ids || ''
      if (isSuperAdmin.value && !roleIdsToSave) {
        roleIdsToSave = originalUserData.value.role_ids || ''
      }

      const params = {
        id: formState.value.id,
        uid: formState.value.uid,
        job_id: formState.value.job_id,
        leader_id: formState.value.leader_id,
        role_ids: roleIdsToSave,
        company_id: formState.value.company_id,
        department_name: formState.value.position_name,
      }

      console.log('保存参数，最终角色ID:', roleIdsToSave)

      UpdateAccountRole(params)
        .then(() => {
          message.success('保存成功')
          emit('query')
          closeDrawer()
        })
        .catch((error: any) => {
          const errorMessage = error?.message || '保存失败'
          message.error(errorMessage)
        })
        .finally(() => {
          submitLoading.value = false
        })
    })
    .catch((error: any) => {
      if (error.errorFields) {
        const firstError = error.errorFields[0]
        switch (firstError.name[0]) {
          case 'real_name':
            message.error('请完善姓名信息')
            break
          case 'uid':
            message.error('请完善账号信息')
            break
          case 'company_id':
            message.error('请完善所属部门信息')
            break
          // case 'role_ids':
          //   message.error('请选择系统角色')
          //   break
          default:
            message.error(firstError.errors[0])
        }
      }
    })
}

// 添加防抖的保存编辑用户函数
const handleSave = createLoadingDebounce(saveEditUserCore, submitLoading, 1000)

// 抽屉状态改变后的回调
const afterOpenChange = (status: boolean) => {
  if (!status) {
    // 只重置未填写的字段，保留已填写的内容
    formRef.value?.resetFields()
  }
}

const onDrop = () => {
  nextTick(() => {
    const el: HTMLElement | null = document.querySelector('.ant-select-selection-overflow')
    if (!el) return
    new Sortable(el, {
      animation: 300,
      handle: '.dragTag',
      delay: 10,
      forceFallback: true,
      onEnd: (item) => {
        const { oldIndex, newIndex } = item
        const currRow = formState.value.department_ids.splice(oldIndex, 1)[0]
        const arr = cloneDeep(formState.value.department_ids)
        arr.splice(newIndex, 0, currRow)

        const currRow1 = formState.value.department.splice(oldIndex, 1)[0]
        const arr1 = cloneDeep(formState.value.department)
        arr1.splice(newIndex, 0, currRow1)

        nextTick(() => {
          formState.value.department_ids = arr
          formState.value.department = arr1
          departmentOption.value = formState.value.department.map((item: any, index: any) => ({
            label: item.name || item.department_name,
            value: item.id,
            index,
          }))
        })
      },
    })
  })
}

onMounted(() => {
  // 预加载角色数据（显示模式），但不显示错误信息，因为用户可能还没有权限
  GetRoleListData().catch((error) => {
    console.warn('预加载角色数据失败，将在用户点击时重新加载:', error)
  })
})

defineExpose({
  showDrawer,
})
</script>

<style lang="scss" scoped>
:deep(.ant-form) {
  .ant-form-item {
    margin-bottom: 16px;
  }
}
</style>
