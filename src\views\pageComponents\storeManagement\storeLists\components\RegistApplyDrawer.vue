<template>
  <a-drawer
    :footer="drawerVisible ? undefined : false"
    v-model:open="drawerVisible"
    width="45vw"
    title="店铺注册申请"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
    @afterOpenChange="formRef.clearValidate()"
    @close="onClose"
  >
    <div class="detailBox">
      <LoadingOutlined v-show="loading" class="loadingIcon" />
      <a-form ref="formRef" :model="editForm" :rules="rules">
        <div class="drawer-title">基础信息</div>
        <a-form-item label="流程标题">
          <span>{{ detail.title }}</span>
        </a-form-item>
        <a-form-item label="流水号">
          <span>--</span>
        </a-form-item>
        <a-form-item label="分部">
          <span>{{ detail.company }}</span>
        </a-form-item>
        <a-form-item label="申请部门">
          <span>{{ detail.department }}</span>
        </a-form-item>
        <a-form-item label="申请人">
          <span>{{ detail.creator }}</span>
        </a-form-item>
        <a-form-item label="申请日期">
          <span>{{ detail.applyDate }}</span>
        </a-form-item>
        <a-form-item label="平台（大类）" name="shop_platform_id">
          <a-select
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="editForm.shop_platform_id"
            placeholder="平台大类"
            @change="changePlatformCategory"
            show-search
            :filter-option="(input, option) => filterOption(input, option)"
          >
            <a-select-option v-for="item in platformLargeCategoriesList" :key="item.value" :value="item.value" :label="item.label">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="平台（小类）" name="shop_platform_subtype_id">
          <a-select
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="editForm.shop_platform_subtype_id"
            placeholder="平台小类"
            @click="clickPlatformSubCategory"
            show-search
            :filter-option="(input, option) => filterOption(input, option)"
            @change="changePlatformSubCategory"
          >
            <a-select-option v-for="item in platformSubCategoriesList" :key="item.value" :value="item.value" :label="item.label">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="注册证件类型">
          <span>{{ registration_document_type?.find((n) => n.value == detail.document_type)?.label || '--' }}</span>
        </a-form-item>
        <a-form-item label="证件可注册次数">
          <span>{{ detail.registration_limit }}</span>
        </a-form-item>
        <a-form-item label="保证金">
          <span>{{ detail.margin }}</span>
        </a-form-item>
        <a-form-item label="店铺名称" name="name">
          <span>{{ `${detail?.platform_sub_category_name}-XXX-` }}</span>
          <a-input v-model:value="editForm.name" class="short" />
        </a-form-item>
        <a-form-item label="站点" name="shop_site_id">
          <a-select
            v-if="isShowShopSite"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            show-search
            v-model:value="editForm.shop_site_id"
            placeholder="请选择站点"
            :filter-option="(input, option) => filterOption(input, option)"
          >
            <a-select-option v-for="item in countryList" :key="item.id" :value="item.id" :label="item.name">{{ item.name }}</a-select-option>
          </a-select>
          <span v-else>--</span>
        </a-form-item>

        <a-form-item label="负责人" name="owner_id">
          <a-form-item-rest>
            <a-select
              :getPopupContainer="(triggerNode) => triggerNode.parentNode"
              v-model:value="detail.sub_company_id"
              placeholder="请选择所属公司"
              @change="changeCompany"
              class="short"
              show-search
              :filter-option="(input, option) => filterOption(input, option)"
              disabled
            >
              <a-select-option v-for="item in companyList" :key="item.value" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
            </a-select>
            <a-tree-select
              show-search
              class="short"
              v-model:value="detail.department_id"
              allow-clear
              :tree-data="departmentList"
              :fieldNames="{
                children: 'childrenList',
                label: 'departmentname',
                value: 'departmentid',
              }"
              :maxTagCount="1"
              :listHeight="400"
              :dropdownMatchSelectWidth="250"
              placeholder="请选择所在部门"
              @click="onClickDepartment"
              @change="changeDepartment"
              treeNodeFilterProp="departmentname"
              :filter-option="(input, option) => filterOption(input, option)"
            />
          </a-form-item-rest>
          <a-select
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="editForm.owner_id"
            placeholder="请选择负责人"
            @click="onClickPrincipal"
            class="short"
            show-search
            :filter-option="(input, option) => filterOption(input, option)"
          >
            <a-select-option v-for="item in userList" :key="item.id" :value="item.id" :label="item.principal">{{ item.principal }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="备注说明" name="remarks">
          <a-textarea v-model:value="editForm.remarks" :rows="4" :maxlength="500" :showCount="true" />
        </a-form-item>
      </a-form>
    </div>
    <template #footer>
      <a-button style="margin-right: 0.8333rem" type="primary" @click="onSubmit" :disabled="btnLoading">提交</a-button>
      <a-button @click="onClose">取消</a-button>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { LoadingOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import { GetCategoryOptions, GetSubCategoryOptions, ApplyStore, GetCountryOptions } from '@/servers/StoreLists'
import { UserInfo } from '@/servers/User'
import { validateStr, filterOption } from '@/utils/index'
import { GetDepartmentTreeList, GetCompanyList, GetUserOptions } from '@/servers/UserManager'
import { GetEnum } from '@/servers/Common'

const emit = defineEmits(['updateStatus', 'success'])
const drawerVisible = ref(false)
const loading = ref(false)
const btnLoading = ref(false)
const formRef = ref()
const editForm = ref<any>({
  name: '',
  shop_site_id: null,
  shop_platform_id: null,
  shop_platform_subtype_id: null,
})
const detail = ref<any>({
  document_type: '',
  registration_limit: '',
  margin: '',
  platform_sub_category_name: '',
})
const platformLargeCategoriesList = ref<any>([])
const platformSubCategoriesList = ref<any>([])
const countryList = ref<any>([])
const companyList = ref<any>([])
const departmentList = ref<any>([])
const userList = ref<any>([])
const rules: Record<string, Rule[]> = ref({
  name: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 50),
      message: '输入内容不可超过50字符',
    },
  ],
  shop_platform_id: [{ required: true, message: '请选择平台大类', trigger: ['change', 'blur'] }],
  shop_platform_subtype_id: [{ required: true, message: '请选择平台小类', trigger: ['change', 'blur'] }],
  owner_id: [{ required: true, message: '请选择负责人', trigger: ['change', 'blur'] }],
  shop_site_id: [{ required: true, message: '请选择站点', trigger: ['change', 'blur'] }],
})
const isShowShopSite = ref(false)

const registration_document_type = ref([])
// 获取所有枚举选项
const getEnum = () => {
  GetEnum().then((res) => {
    registration_document_type.value = res.data.data.registration_document_type
  })
}

const onSubmit = async () => {
  try {
    await formRef.value.validateFields()
    submit()
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
const submit = () => {
  const obj = JSON.parse(JSON.stringify(editForm.value))
  obj.shop_name = `${detail.value?.platform_sub_category_name}-XXX-${editForm.value.name}`
  if (obj.shop_site_id === null) {
    delete obj.shop_site_id
  }
  btnLoading.value = true
  ApplyStore(obj).then(() => {
    message.success('店铺注册流程已提交，请耐心等待注册完成！')
    onClose()
    emit('success')
  })
}

const changePlatformCategory = (id) => {
  getPlatFormSubList(id)
  if (editForm.value.shop_platform_subtype_id) {
    editForm.value.shop_platform_subtype_id = null
    detail.value.document_type = null
    detail.value.registration_limit = null
    detail.value.margin = null
  }
}

const clickPlatformSubCategory = () => {
  if (!editForm.value.shop_platform_id) {
    message.info('请先选择平台大类')
  }
}

const changePlatformSubCategory = () => {
  if (editForm.value.shop_platform_subtype_id) {
    const item = platformSubCategoriesList.value.find((item) => item.value == editForm.value.shop_platform_subtype_id)
    detail.value.document_type = item.document_type
    detail.value.registration_limit = item.registration_limit
    detail.value.margin = item.margin
    detail.value.platform_sub_category_name = item.name
    if (item.register_designated_site === 1) {
      isShowShopSite.value = true
      rules.value.shop_site_id = [{ required: true, message: '请选择站点', trigger: ['change', 'blur'] }]
    } else {
      isShowShopSite.value = false
      editForm.value.shop_site_id = null
      delete rules.value.shop_site_id
    }
  }
}

// 关联大类下拉框
const getPlatformLargeList = () => {
  const params = { status: 1, exclude_alibaba: 1 }
  GetCategoryOptions(params).then((res) => {
    platformLargeCategoriesList.value = res.data.map((item) => {
      return { label: item.name, value: item.id }
    })
  })
}
// 关联小类下拉框
const getPlatFormSubList = (id) => {
  const params = { categories: id, exclude_alibaba: 1, status: 1 }
  GetSubCategoryOptions(params).then((res) => {
    platformSubCategoriesList.value = res.data.map((item) => {
      return { ...item, label: item.name, value: item.id }
    })
  })
}

const onClickDepartment = () => {
  if (!detail.value.sub_company_id) {
    message.info('请先选择所属公司')
  }
}
// 获取公司树状下拉框
const getCompanyList = () => {
  GetCompanyList().then((res) => {
    res.data.forEach((x) => {
      x.label = x.company_name
      x.value = x.company_id
    })
    companyList.value = res.data
  })
}

// 获取部门树状下拉框(内部)
const getDepartmentTreeList = () => {
  if (!detail.value.sub_company_id) {
    return
  }
  GetDepartmentTreeList({ subcompanyid1: detail.value.sub_company_id }).then((res) => {
    departmentList.value = res.data
  })
}

// 获取用户下拉数据
const getUserList = async () => {
  const res = await GetUserOptions({ subcompanyid1: detail.value.sub_company_id, departmentid: detail.value.department_id })
  userList.value = res.data
}

const onClickPrincipal = () => {
  if (!detail.value.department_id) {
    message.info('请先选择所在部门')
  }
}

const changeCompany = () => {
  getDepartmentTreeList()
  detail.value.department_id = ''
  editForm.value.owner_id = ''
  userList.value = []
}

const changeDepartment = async () => {
  if (!detail.value.department_id) {
    return
  }
  editForm.value.owner_id = ''
  getUserList()
}

const onClose = () => {
  formRef.value.resetFields()
  drawerVisible.value = false
  btnLoading.value = false
}
const open = () => {
  drawerVisible.value = true
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const applyDate = `${year}-${month}-${day}`
  detail.value.applyDate = applyDate

  UserInfo().then((res) => {
    detail.value.creator = `${res.data.user_name}/${res.data.real_name}`
    detail.value.company = res.data.company
    detail.value.department = res.data.department
    detail.value.title = `店铺注册流程-${res.data.real_name}-${applyDate}`
    detail.value.sub_company_id = res.data.company_id
    detail.value.department_id = res.data.department_id
    getDepartmentTreeList()
    getUserList()
  })
}
onMounted(() => {
  getPlatformLargeList()
  getCompanyList()
  getEnum()
  GetCountryOptions({ status: 1 }).then((res) => {
    countryList.value = res.data
  })
})
// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 110px;
    min-width: 110px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.detailTitle {
  padding-left: 12px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #000;
}

.w350 {
  width: 350px;
}

.w250 {
  width: 250px;
}

.w150 {
  width: 150px;
}

.w200 {
  width: 200px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: #00000080;
  white-space: nowrap;
}

.detailValueDescription {
  font-size: 12px;
  color: #00000080;
}

.detailBox {
  padding-top: 12px;

  .loadingIcon {
    font-size: 30px;
    color: #1890ff;
  }
}

.ant-input-number {
  width: 100%;
}

.short {
  width: 15.8rem;
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
