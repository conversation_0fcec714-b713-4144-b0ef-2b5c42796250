<template>
  <!-- 切换账号菜单项 - 用于嵌入到其他下拉菜单中 -->
  <div class="switch-account-menu-item" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
    <div class="menu-item-content">
      <i class="iconfont icon-qiehuan"></i>
      切换账号
      <i class="iconfont icon-youjiantou arrow-icon"></i>
    </div>
    

    <!-- 子菜单 -->
    <div v-if="showSubMenu" class="account-submenu" :style="submenuStyle" @mouseenter="handleSubMenuEnter" @mouseleave="handleSubMenuLeave">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <a-spin size="small" />
        <span class="loading-text">正在获取账号列表...</span>
      </div>

      <!-- 可切换账号列表 -->
      <div v-else-if="switchableAccounts.length > 0" class="account-list">
        <div v-for="account in switchableAccounts" :key="account.account_id"
             class="account-item"
             :class="{ 'switching': switchLoading && selectedAccountId === account.account_id }"
             @click="handleSwitchToAccount(account)">
          <div class="account-avatar">
            <div class="avatar-circle">
             <!-- 企业账号（account_type: 0）的icon逻辑 -->
    <template v-if="account.account_type === 0">
      {{ getEnterpriseIconChar(account) }}
    </template>
    <!-- 非企业账号保持原有逻辑 -->
    <template v-else>
      {{ account.real_name?.charAt(0) || '用' }}
    </template>
            </div>
          </div>
          <div class="account-info">
            <div class="account-name">{{ account.real_name }}</div>
            <div class="account-company" v-if="getCompanyDisplay(account)">{{ getCompanyDisplay(account) }}</div>
            <div class="account-department" v-if="getDepartmentDisplay(account)">
              {{ getDepartmentDisplay(account) }}
            </div>
          </div>
          <div class="switch-action" v-if="switchLoading && selectedAccountId === account.account_id">
            <a-spin size="small" />
          </div>
        </div>
     <div class="personal-center-btn flex items-center justify-center gap-2 h-50 px-4 py-3 border-t border-gray-300 border-opacity-80 text-primary cursor-pointer hover:bg-gray-50 transition-all duration-200" @click="handleUserCenter">
  个人中心(账号关联/绑定/修改密码)
  <RightOutlined class="inline-flex items-center justify-center mt-1.5 align-baseline" style="font-size: 1.1em;" />
</div>
      </div>

      <!-- 无可切换账号 -->
      <div v-else class="no-accounts">
        <div class="no-accounts-text">暂无其他可切换的账号</div>
      </div>
    </div>
    
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onUnmounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { RightOutlined, SwapLeftOutlined } from '@ant-design/icons-vue'
import { GetUserAccountList, Switch } from '@/servers/UmcAuth'
import { useRouter } from 'vue-router'

const router = useRouter()

const showSubMenu = ref(false)
const loading = ref(false)
const switchLoading = ref(false)
const accountList = ref<any[]>([])
const selectedAccountId = ref<string>('')
const currentUser = ref<any>(null)
const submenuStyle = ref({})
const hasLoadedAccountList = ref(false) // 添加标记，避免重复加载
let hoverTimer: NodeJS.Timeout | null = null
let leaveTimer: NodeJS.Timeout | null = null

// 计算可切换的账号列表（排除当前账号）
const switchableAccounts = computed(() => {
  return accountList.value.filter((account) => account.account_id !== currentUser.value?.account_id)
})

// 获取企业账号的icon字符（优先单位首字符，无单位则取企业首字符）
const getEnterpriseIconChar = (account: any): string => {
  // 优先使用 unit 字段（单位信息）
  const unitName = cleanValue(account.unit)
  if (unitName) {
    return unitName.charAt(0) // 单位名称首字符
  }
  
  // 无单位时取企业名称首字符
  const companyName = cleanValue(account.company) || ''
  return companyName.charAt(0) || '企' // 兜底显示"企"
}

// 获取企业/单位名称显示（优先单位，无单位则显示企业）
const getCompanyDisplay = (account: any): string => {
  // 优先显示单位信息
  const unitName = cleanValue(account.unit)
  if (unitName) {
    return unitName
  }
  
  // 无单位时显示企业信息
  const companyName = cleanValue(account.company)
  return companyName || ''
}

// 检查并清理字段值（排除空字符串、null、undefined）
const cleanValue = (value: any) => {
  if (value === null || value === undefined || value === '') {
    return null
  }
  return typeof value === 'string' ? value.trim() : value
}

// 获取部门显示文本
const getDepartmentDisplay = (account: any) => {
  // 优先使用 department 字段
  const department = cleanValue(account.department)
  
  // 如果有部门数组，尝试获取部门名称
  if (account.department && Array.isArray(account.department) && account.department.length > 0) {
    const deptInfo = account.department[0]
    const deptName = cleanValue(deptInfo.name) || cleanValue(deptInfo.department_name)
    if (deptName) {
      return deptName
    }
  }

  // 使用字符串字段
  if (department) {
    return department
  }

  // 如果所有字段都为空，返回空字符串
  return ''
}

// 计算账号信息的丰富程度
const getAccountInfoRichness = (accounts: any[]) => {
  if (accounts.length === 0) return 0

  let totalScore = 0
  accounts.forEach(account => {
    let score = 0

    // 基础信息（必有）
    if (cleanValue(account.real_name)) score += 1

    // 公司/单位信息
    if (getCompanyDisplay(account)) score += 1

    // 部门信息
    const deptDisplay = getDepartmentDisplay(account)
    if (deptDisplay) score += 1

    // 职位信息
    if (cleanValue(account.position_name) || cleanValue(account.jobtitlename)) score += 1

    totalScore += score
  })

  // 返回平均信息丰富度（1-4分）
  return totalScore / accounts.length
}

// 获取当前用户信息
const getCurrentUser = () => {
  try {
    const userData = localStorage.getItem('userData')
    if (userData) {
      currentUser.value = JSON.parse(userData)
    }
  } catch (error) {
    console.error('获取当前用户信息失败:', error)
  }
}

// 在 setup() 函数中定义方法
const handleUserCenter = () => {
  try {
    // 从本地存储获取 userData
    const userData = localStorage.getItem('userData');
    if (!userData) {
      message.error('未找到用户登录信息');
      return;
    }
    
    // 解析 JSON
    const parsedData = JSON.parse(userData);
    
    // 检查 umc_url 是否存在
    if (!parsedData.umc_url) {
      message.error('未找到个人中心链接');
      return;
    }
    
    // 跳转到个人中心
    window.location.href = parsedData.umc_url;
    
  } catch (error) {
    console.error('获取个人中心链接失败:', error);
    // message.error('获取个人中心链接失败');
  }
}

// 获取用户账号列表
const fetchAccountList = async () => {
  // 如果已经加载过且不是强制刷新，直接返回
  if (hasLoadedAccountList.value && accountList.value.length > 0) {
    return
  }

  loading.value = true
  try {
    const res = await GetUserAccountList()
    if (res.success && res.data) {
      accountList.value = res.data
      hasLoadedAccountList.value = true
    } else {
      message.error('获取账号列表失败')
      accountList.value = []
    }
  } catch (error) {
    console.error('获取账号列表失败:', error)
    message.error('获取账号列表失败')
    accountList.value = []
  } finally {
    loading.value = false
  }
}

// 计算子菜单位置
const calculateSubmenuPosition = (target?: HTMLElement) => {
  // 如果没有传入target，尝试获取菜单项元素
  if (!target) {
    target = document.querySelector('.switch-account-menu-item') as HTMLElement
  }

  if (!target) {
    console.error('无法找到菜单项元素')
    return
  }

  const rect = target.getBoundingClientRect()

  // 动态计算子菜单宽度和高度
  const baseSubmenuWidth = 320 // 基础子菜单宽度
  const accountCount = switchableAccounts.value.length

  // 根据账号数量动态调整子菜单高度和位置
  let submenuHeight = 0
  if (loading.value) {
    submenuHeight = 80 // 加载状态的高度
  } else if (accountCount === 0) {
    submenuHeight = 60 // 无账号状态的高度
  } else {
    // 每个账号项约70px高度，加上padding
    submenuHeight = Math.min(accountCount * 70 + 16, 400) // 最大高度400px
  }

  // 计算垂直位置，确保子菜单不会超出视窗
  const viewportHeight = window.innerHeight
  let topPosition = rect.top

  // 如果子菜单会超出底部，向上调整位置
  if (topPosition + submenuHeight > viewportHeight) {
    topPosition = Math.max(10, viewportHeight - submenuHeight - 10)
  }

  // 水平位置计算，根据账号信息丰富程度动态调整距离
  const accountRichness = getAccountInfoRichness(switchableAccounts.value)
  let leftOffset

  if (accountRichness >= 3) {
    // 信息丰富（有公司、部门等），使用较大距离
    leftOffset = 43
  } else if (accountRichness >= 2) {
    // 信息中等，使用中等距离
    leftOffset = 30
  } else {
    // 信息较少（只有姓名等基础信息），使用负值让子菜单更贴近甚至重叠
    leftOffset = -28
  }

  submenuStyle.value = {
    position: 'fixed',
    top: `${topPosition}px`,
    left: `${rect.left - baseSubmenuWidth - leftOffset}px`,
    zIndex: 9999,
  }
}

// 处理鼠标进入菜单项
const handleMouseEnter = (event: MouseEvent) => {
  // 清除离开定时器
  if (leaveTimer) {
    clearTimeout(leaveTimer)
    leaveTimer = null
  }

  // 如果子菜单已经显示，不需要重新设置定时器
  if (showSubMenu.value) {
    return
  }

  // 立即获取元素位置，避免延迟后事件失效
  const target = event.currentTarget as HTMLElement

  // 设置悬停定时器，延迟显示子菜单
  hoverTimer = setTimeout(async () => {
    getCurrentUser()
    await fetchAccountList()

    // 等待下一个tick，确保DOM更新完成后再计算位置
    await nextTick()
    calculateSubmenuPosition(target)
    showSubMenu.value = true
  }, 200) // 200ms延迟，避免误触发
}

// 处理鼠标离开菜单项
const handleMouseLeave = () => {
  // 清除悬停定时器
  if (hoverTimer) {
    clearTimeout(hoverTimer)
    hoverTimer = null
  }

  // 设置离开定时器，延迟隐藏子菜单
  leaveTimer = setTimeout(() => {
    showSubMenu.value = false
  }, 300) // 300ms延迟，给用户时间移动到子菜单
}

// 处理鼠标进入子菜单
const handleSubMenuEnter = () => {
  // 清除离开定时器
  if (leaveTimer) {
    clearTimeout(leaveTimer)
    leaveTimer = null
  }
}

// 处理鼠标离开子菜单
const handleSubMenuLeave = () => {
  // 设置延迟隐藏，避免鼠标快速移动时误关闭
  leaveTimer = setTimeout(() => {
    showSubMenu.value = false
  }, 100) // 短延迟，给用户时间重新进入
}

// 创建全屏loading
const createFullscreenLoading = () => {
  const loadingDiv = document.createElement('div')
  loadingDiv.id = 'switch-account-loading'

  // 添加CSS动画样式
  const style = document.createElement('style')
  style.textContent = `
    @keyframes fadeIn {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }

    @keyframes spin {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }

    .switch-loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(4px);
      z-index: 800;
      display: flex;
      align-items: center;
      justify-content: center;
      animation: fadeIn 0.2s ease-out;
    }

    .switch-loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;
      padding: 32px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      border: 1px solid #f0f0f0;
    }

    .switch-loading-spinner {
      width: 32px;
      height: 32px;
      border: 3px solid #f0f0f0;
      border-top: 3px solid #1890ff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .switch-loading-text {
      font-size: 14px;
      color: #666;
      font-weight: 400;
      margin: 0;
    }
  `
  document.head.appendChild(style)

  loadingDiv.innerHTML = `
    <div class="switch-loading-overlay">
      <div class="switch-loading-content">
        <div class="switch-loading-spinner"></div>
        <div class="switch-loading-text">正在切换账号...</div>
      </div>
    </div>
  `

  document.body.appendChild(loadingDiv)
  return { loadingDiv, style }
}

// 移除全屏loading
const removeFullscreenLoading = () => {
  const loadingDiv = document.getElementById('switch-account-loading')
  if (loadingDiv) {
    // 添加淡出动画
    const overlay = loadingDiv.querySelector('.switch-loading-overlay')
    if (overlay) {
      overlay.style.animation = 'fadeIn 0.15s ease-out reverse'
      setTimeout(() => {
        if (loadingDiv.parentNode) {
          document.body.removeChild(loadingDiv)
        }
        // 移除样式
        const style = document.querySelector('style[data-loading-style]')
        if (style) {
          document.head.removeChild(style)
        }
      }, 150)
    } else {
      document.body.removeChild(loadingDiv)
    }
  }
}

// 直接切换到指定账号
const handleSwitchToAccount = async (account: any) => {
  if (account.account_id === currentUser.value?.account_id) {
    return // 不能切换到当前账号
  }

  // 防止重复点击
  if (switchLoading.value) {
    return
  }

  // 设置当前正在切换的账号ID，用于显示loading
  selectedAccountId.value = account.account_id
  switchLoading.value = true

  // 创建全屏loading
  const { loadingDiv, style } = createFullscreenLoading()
  style.setAttribute('data-loading-style', 'true')

  try {
    const res = await Switch({ id: account.account_id })
    if (res.success && res.data) {
     setTimeout(() => {
  message.success('切换账号成功，正在刷新页面...')
}, 100) // 延迟 100ms，确保 loading 已渲染后再显示提示

      // 隐藏子菜单
      showSubMenu.value = false

      // 重置账号列表加载状态，下次需要重新加载
      hasLoadedAccountList.value = false
      accountList.value = []

      // Switch接口已经返回了完整的用户信息，包括权限信息
      // 添加登录时间戳用于token过期检查
      const newUserData = {
        ...res.data,
        login_time: Math.floor(Date.now() / 1000),
      }

      // 更新localStorage
      localStorage.setItem('userData', JSON.stringify(newUserData))

      // 清除导航页面数组，让系统重新初始化
      localStorage.removeItem('navPageArr')

      // 清除其他可能的缓存数据
      localStorage.removeItem('version')

      // 延迟刷新页面，避免权限问题
      setTimeout(() => {
        window.location.reload()
      }, 500)
    } else {
      message.error(res.message || '切换账号失败')
      removeFullscreenLoading()
    }
  } catch (error) {
    console.error('切换账号失败:', error)
    message.error('切换账号失败')
    removeFullscreenLoading()
  } finally {
    switchLoading.value = false
    selectedAccountId.value = ''
  }
}

// 初始化组件时获取当前用户信息
const init = () => {
  getCurrentUser()
}

// 组件挂载时初始化
init()

// 清理定时器
const cleanup = () => {
  if (hoverTimer) {
    clearTimeout(hoverTimer)
    hoverTimer = null
  }
  if (leaveTimer) {
    clearTimeout(leaveTimer)
    leaveTimer = null
  }
}

// 组件卸载时清理
onUnmounted(() => {
  cleanup()
  removeFullscreenLoading() // 清理可能残留的全屏loading
})

// 暴露方法给父组件（保持兼容性）
defineExpose({
  open: () => {
    // 兼容旧的调用方式，现在直接显示子菜单
    getCurrentUser()
    // 强制刷新账号列表
    hasLoadedAccountList.value = false
    fetchAccountList()
    showSubMenu.value = true
  },
})
</script>

<style lang="scss" scoped>
.switch-account-menu-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: 12px 20px;
  padding-left: 24px;
  color: #666;
  cursor: pointer;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  transition: all 0.3s;

  &:hover {
    color: #409eff;
    background: #eef2fa;
  }

  .menu-item-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    i {
      margin-right: 12px;
    }

    .arrow-icon {
      margin-right: 0;
      margin-left: auto;
      font-size: 12px;
    }
  }

  // 子菜单样式
  .account-submenu {
    position: relative;
    min-width: 280px;
    max-width: 350px;
    min-height: 60px; // 确保最小高度，避免过小
    background: white;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgb(0 0 0 / 15%);

    // 右侧小三角形指示器 - 固定在合适位置
    &::after {
      position: absolute;
      top: 24px;
      right: -8px;
      z-index: 10001;
      width: 0;
      height: 0;
      content: '';
      border-top: 8px solid transparent;
      border-bottom: 8px solid transparent;
      border-left: 8px solid white;
    }

    // 右侧小三角形边框 - 固定在合适位置
    &::before {
      position: absolute;
      top: 23px;
      right: -9px;
      z-index: 10000;
      width: 0;
      height: 0;
      content: '';
      border-top: 9px solid transparent;
      border-bottom: 9px solid transparent;
      border-left: 9px solid #e8e8e8;
    }

    // 账号列表
    .account-list {
      padding: 8px;
      max-height: 320px; // 限制最大高度
      overflow-y: auto;

      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a8a8a8;
        }
      }

      .account-item {
        display: flex;
        align-items: center;
        padding: 12px;
        margin-bottom: 4px;
        cursor: pointer;
        border-radius: 6px;
        transition: all 0.2s;

        &:hover {
          background: #f8f9fa;
        }

        &:last-child {
          margin-bottom: 0;
        }

        // 切换中的状态样式
        &.switching {
          opacity: 0.7;
          pointer-events: none;
          background: #f0f8ff;
        }

        .account-avatar {
          margin-right: 12px;

          .avatar-circle {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            font-size: 14px;
            font-weight: 500;
            color: white;
            background: #1890ff;
            border-radius: 50%;
          }
        }

        .account-info {
          flex: 1;
          min-width: 0;

          .account-name {
            margin-bottom: 2px;
            font-size: 14px;
            font-weight: 500;
            line-height: 1.2;
            color: #333;
          }

          .account-company {
            margin-bottom: 1px;
            font-size: 12px;
            line-height: 1.2;
            color: #666;

            // 当没有部门信息时，减少底部间距
            &:last-child {
              margin-bottom: 0;
            }
          }

          .account-department {
            font-size: 11px;
            line-height: 1.2;
            color: #999;
            margin-bottom: 0;
          }
        }

        .switch-action {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;
          margin-left: 8px;
        }
      }
    }

    // 无账号状态
    .no-accounts {
      padding: 20px;
      text-align: center;

      .no-accounts-text {
        font-size: 13px;
        color: #999;
      }
    }

    // 加载状态
    .loading-container {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;

      .loading-text {
        margin-left: 8px;
        font-size: 13px;
        color: #666;
      }
    }
  }
}
// 穿透 scoped 限制，修改全局 message 组件样式
:deep(.ant-message-notice-wrapper) {
  z-index: 10010 !important; // 高于 loading 的 z-index: 10000
}
</style>
