<template>
  <a-config-provider :locale="zhCN" :theme="theme">
    <router-view />
  </a-config-provider>
</template>

<script setup>
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import { RouterView } from 'vue-router'
import { Watermark } from 'watermark-js-plus'
import eventBus from '@/utils/eventBus'
import { GetSystemWatermark } from '@/servers/WaterMark'

let watermark = null
dayjs.locale('zh-cn')
if (import.meta.env.VITE_APP_ENV === 'development') {
  console.log('当前请求地址：', import.meta.env.VITE_APP_BASE_API)
}

const theme = ref({
  token: {
    colorPrimary: '#409EFF',
    borderRadius: 4,
    // paddingContentHorizontal: 12,
    fontSize: 12,
    controlHeight: 28,
    marginLG: 20,
  },
  components: {
    Drawer: {
      fontWeightStrong: 400,
      fontSizeLG: 14,
    },
    Table: {
      colorTextHeading: '#606266',
    },
    Modal: {
      fontSizeHeading5: 14,
    },
    Form: {
      colorTextHeading: '#666666',
    },
    Button: {
      paddingContentHorizontal: 20,
    },
  },
})
const calculateRotatedContainerFromRichText = (richText, fontSize) => {
  const rotationAngle = 25
  const container = document.createElement('div')
  container.style.position = 'absolute'
  container.style.visibility = 'hidden'
  container.style.whiteSpace = 'nowrap'
  container.style.margin = '0'
  container.style.padding = '0'
  container.style.lineHeight = 'normal'
  container.style.fontSize = `${fontSize}px`
  container.innerHTML = richText
  document.body.appendChild(container)
  const boundingBox = container.getBoundingClientRect()
  const contentWidth = boundingBox.width
  const contentHeight = boundingBox.height
  document.body.removeChild(container)
  const theta = (rotationAngle * Math.PI) / 180
  const rotatedWidth = contentHeight * Math.abs(Math.sin(theta)) + contentWidth * Math.abs(Math.cos(theta))
  const rotatedHeight = contentHeight * Math.abs(Math.cos(theta)) + contentWidth * Math.abs(Math.sin(theta))
  return {
    width: Math.ceil(rotatedWidth),
    height: Math.ceil(rotatedHeight),
  }
}
const setWatermark = async (data = null) => {
  console.log('调用了')
  let content = data ? data.textInfo : null
  let space = data ? (data.denseType == 3 ? 1.5 : data.denseType == 2 ? 2 : 2.5) : null
  let color = data ? (data.colorType == 1 ? 'rgba(0,0,0,0.05)' : data.colorType == 2 ? 'rgba(0,0,0,0.1)' : 'rgba(0,0,0,0.15)') : null
  let fontSize = data ? (data.sizeType == 1 ? '12' : data.sizeType == 2 ? '14' : '16') : null
  try {
    if (!content) {
      const res = await GetSystemWatermark()
      content = res.data.textInfo
      space = res.data.denseType == 3 ? 1.5 : res.data.denseType == 2 ? 2 : 2.5
      color = res.data.colorType == 1 ? 'rgba(0,0,0,0.05)' : res.data.colorType == 2 ? 'rgba(0,0,0,0.1)' : 'rgba(0,0,0,0.15)'
      fontSize = res.data.sizeType == 1 ? '12' : res.data.sizeType == 2 ? '14' : '16'
    }
    if (content) {
      const domStr = `<div style="text-align:left;font-size:${fontSize}px;color:${color};white-space:nowrap;">${content}</div>`
      const wh = calculateRotatedContainerFromRichText(domStr, fontSize)
      watermark = new Watermark({
        width: wh.width * space,
        height: wh.height * space,
        rotate: 25,
        contentType: 'rich-text',
        content: domStr,
        globalAlpha: 1,
        // "zIndex": 1000
      })
      watermark.create()
    }
  } catch (error) {
    console.log(error)
  }
}
if (localStorage.getItem('userData')) {
  setWatermark()
}
eventBus.on('setWatermark', (data = null) => {
  try {
    watermark.destroy()
  } catch (error) {
    //
  }
  setWatermark(data)
})
eventBus.on('clearWatermark', () => {
  try {
    watermark.destroy()
  } catch (error) {
    //
  }
})
</script>

<style>
.ant-drawer .ant-drawer-footer {
  padding: 8px 24px;
}

.vxe-cell .ant-btn {
  height: 24px;
  padding: 0 12px;
}

.ant-drawer-content .ant-btn-sm {
  height: 24px;
}
</style>
