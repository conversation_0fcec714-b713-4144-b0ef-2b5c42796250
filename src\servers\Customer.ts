// 客户接口
import { request } from './request'

// 新增客户
export const Increase = (data) => {
  return request({ url: '/api/Customer/Add', data })
}
// 编辑客户
export const Edit = (data) => {
  return request({ url: '/api/Customer/Update', data })
}
// 批量停用/启用客户
export const UpdateStatus = (data) => {
  return request({ url: '/api/Customer/UpdateStatus', data })
}
// 删除客户
export const Delete = (data) => {
  return request({ url: '/api/Customer/Delete', data })
}
// 查询客户列表
export const GetList = (data) => {
  return request({ url: '/api/Customer/List', data })
}
// 获取客户下拉框
export const GetCustomerSelectOption = (data) => {
  return request({ url: '/api/Customer/GetCustomerSelectOption', data })
}
// 获取客户操作日志
export const Log = (data) => {
  return request({ url: '/api/Customer/Log', data })
}

// 获取客户详情
export const Detail = (data) => {
  return request({ url: '/api/Customer/Detail', data })
}

// 获取部门树状数据
export const GetDepartmentList = (data) => {
  return request({ url: '/api/Customer/DepartmentList', data })
}
// 新增部门
export const IncreaseDepartment = (data) => {
  return request({ url: '/api/Customer/DepartmentAdd', data })
}
// 移动部门
export const MoveDepartment = (data) => {
  return request({ url: '/api/Customer/DepartmentEditByLevel', data })
}
// 删除部门
export const DelDepartment = (data) => {
  return request({ url: '/api/Customer/DepartmentDelete', data })
}
// 修改部门
export const EditDepartment = (data) => {
  return request({ url: '/api/Customer/DepartmentEditByName', data })
}
