<template>
  <a-drawer
    :footer="drawerVisible ? undefined : false"
    v-model:open="drawerVisible"
    width="520"
    :title="formType == 'add' ? '新建平台小类' : '编辑平台小类'"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
    @afterOpenChange="formRef.clearValidate()"
    @close="onClose"
  >
    <div class="detailBox">
      <LoadingOutlined v-show="loading" class="loadingIcon" />
      <a-form ref="formRef" :model="editForm" :rules="rules">
        <a-form-item label="状态" name="status">
          <a-switch id="status" v-model:checked="[false, true][editForm.status]" @click="editForm.status = editForm.status ? 0 : 1" checked-children="启用" un-checked-children="停用" />
        </a-form-item>
        <a-form-item label="小类名称" name="name">
          <a-input v-model:value="editForm.name" placeholder="请输入平台小类名称" />
        </a-form-item>
        <a-form-item label="关联大类" name="platform_category_id">
          <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="editForm.platform_category_id" placeholder="请选择平台大类">
            <a-select-option v-for="item in platformLargeCategoriesList" :key="item.id" :value="item.id">
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="品牌授权" name="brand_authorization">
          <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="editForm.brand_authorization" placeholder="请选择">
            <a-select-option v-for="item in brand_authorizationList" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="店铺类型" name="store_type">
          <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="editForm.store_type" placeholder="请选择">
            <a-select-option v-for="item in storeTypeList" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="注册证件类型" name="document_type">
          <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="editForm.document_type" placeholder="请选择">
            <a-select-option v-for="item in documentTypeList" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="可注册次数" name="registration_limit">
          <a-input-number v-model:value="editForm.registration_limit" placeholder="请输入可注册次数" :min="1" :max="999999" :precision="0" />
        </a-form-item>
        <a-form-item label="保证金" name="is_margin">
          <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="editForm.is_margin" placeholder="请选择" @change="changeValue('is_margin', editForm.is_margin)">
            <a-select-option v-for="item in bondList" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="注册指定站点" name="register_designated_site">
          <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="editForm.register_designated_site" placeholder="请选择">
            <a-select-option :value="1">是</a-select-option>
            <a-select-option :value="2">否</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="保证金缴纳节点" name="margin_payment_node" v-if="editForm.is_margin == 1" required>
          <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="editForm.margin_payment_node" placeholder="请选择">
            <a-select-option v-for="item in margin_payment_nodeList" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="保证金金额" name="margin" v-if="editForm.is_margin == 1" required>
          <a-input-number v-model:value="editForm.margin" placeholder="请输入保证金金额" :min="0" :max="999999" :precision="2" />
        </a-form-item>
        <a-form-item label="二次缴费" name="second_payment">
          <a-select
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="editForm.second_payment"
            placeholder="请选择"
            @change="changeValue('second_payment', editForm.is_margin)"
          >
            <a-select-option v-for="item in secondPaymentList" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="缴费类型" name="payment_type" v-if="editForm.second_payment == 1" required>
          <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="editForm.payment_type" placeholder="请选择">
            <a-select-option v-for="item in payment_typeList" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="缴费金额" name="payment_amount" v-if="editForm.second_payment == 1" required>
          <a-input-number v-model:value="editForm.payment_amount" placeholder="请输入缴费金额" :min="0" :max="999999" :precision="2" />
        </a-form-item>
      </a-form>
    </div>
    <template #footer>
      <a-button style="margin-right: 0.8333rem" type="primary" @click="onSubmit">确认</a-button>
      <a-button @click="onClose">取消</a-button>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { Details, Add, Update } from '@/servers/platformSubCateg'
import { LoadingOutlined } from '@ant-design/icons-vue'
import { validateStr } from '@/utils/index'
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import { GetList } from '@/servers/PlatformLarge'
import { GetEnum } from '@/servers/Common'

const emit = defineEmits(['updateStatus', 'success'])
const drawerVisible = ref(false)
const loading = ref(false)
const formRef = ref()
const formType = ref('edit')
const oldStatus = ref(null)
const editForm = ref<any>({
  id: null,
  name: '',
  status: 1,
  brand_authorization: null,
  is_margin: 1,
  margin_payment_node: null,
  margin: null,
  second_payment: 0,
  payment_type: 1,
  payment_amount: '',
  registration_limit: '',
  store_type: null,
  document_type: null,
  platform_category_id: null,
})
const platformLargeCategoriesList = ref<any>([])
const storeTypeList = ref<any>([])
const documentTypeList = ref<any>([])
const bondList = ref<any>([])
const secondPaymentList = ref<any>([])
const brand_authorizationList = ref<any>([])
const margin_payment_nodeList = ref<any>([])
const payment_typeList = ref<any>([])
let rules: Record<string, Rule[]> = {
  name: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 50),
      message: '输入内容不可超过50字符',
    },
  ],
  platform_category_id: [{ required: true, message: '请选择关联大类', trigger: ['change', 'blur'] }],
  store_type: [{ required: true, message: '请选择店铺类型', trigger: ['change', 'blur'] }],
  document_type: [{ required: true, message: '请选择注册证件类型', trigger: ['change', 'blur'] }],
  registration_limit: [{ required: true, message: '请输入可注册次数', trigger: ['change', 'blur'] }],
  is_margin: [{ required: true, message: '请选择保证金', trigger: ['change', 'blur'] }],
  second_payment: [{ required: true, message: '请选择二次缴费', trigger: ['change', 'blur'] }],
}
const bondRules: Record<string, Rule[]> = {
  margin_payment_node: [{ required: true, message: '请选择保证金缴纳节点', trigger: ['change', 'blur'] }],
  margin: [{ required: true, message: '请输入保证金金额', trigger: ['change', 'blur'] }],
}

const secondPayRules: Record<string, Rule[]> = {
  payment_type: [{ required: true, message: '请选择缴费类型', trigger: ['change', 'blur'] }],
  payment_amount: [{ required: true, message: '请输入缴费金额', trigger: ['change', 'blur'] }],
}

const changeValue = (field, value) => {
  switch (field) {
    case 'is_margin':
      if (value == 1) {
        rules = { ...rules, ...bondRules }
      } else {
        delete rules.margin_payment_node
        delete rules.margin
      }
      break
    case 'second_payment':
      if (value == 1) {
        rules = { ...rules, ...secondPayRules }
      } else {
        delete rules.payment_type
        delete rules.payment_amount
      }
      break
    default:
      break
  }
}

const onSubmit = async () => {
  try {
    await formRef.value.validateFields()
    switch (formType.value) {
      case 'add':
        addSubmit()
        break
      case 'edit':
        updateSubmit()
        break
      default:
        break
    }
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
// 新增平台小类
const addSubmit = () => {
  const obj = JSON.parse(JSON.stringify(editForm.value))
  Add(obj).then(() => {
    message.success('新增成功')
    onClose()
    emit('success')
  })
}
// 编辑平台小类
const updateSubmit = () => {
  const fn = () => {
    const obj = JSON.parse(JSON.stringify(editForm.value))
    Update(obj).then(() => {
      message.success('修改成功')
      onClose()
      emit('success')
    })
  }
  if (editForm.value.status == 0 && oldStatus.value == 1) {
    emit('updateStatus', {
      id: editForm.value.id,
      status: oldStatus.value,
      fn,
    })
  } else {
    fn()
  }
}
// 关联大类下拉框
const getPlatformLargeList = () => {
  const params = {}
  GetList(params).then((res) => {
    platformLargeCategoriesList.value = res.data.list
  })
}
// 获取枚举
const getEnum = () => {
  GetEnum().then((res) => {
    storeTypeList.value = res.data.data.store_type
    documentTypeList.value = res.data.data.registration_document_type
    bondList.value = res.data.data.is_margin
    secondPaymentList.value = res.data.data.second_payment
    brand_authorizationList.value = res.data.data.brand_authorization
    margin_payment_nodeList.value = res.data.data.margin_payment_node
    payment_typeList.value = res.data.data.payment_type
  })
}

const onClose = () => {
  formRef.value.resetFields()
  editForm.value.margin_payment_node = null
  editForm.value.margin = null
  drawerVisible.value = false
}
const open = (id, type) => {
  drawerVisible.value = true
  formType.value = type
  if (type == 'add') {
    editForm.value.status = 1
    editForm.value.second_payment = 0
  } else if (id) {
    loading.value = true
    editForm.value = {}
    Details({ id })
      .then((res) => {
        editForm.value = res.data || {}
        oldStatus.value = res.data.status
        loading.value = false
      })
      .catch(() => {
        loading.value = false
      })
  }
}
onMounted(() => {
  getPlatformLargeList()
  getEnum()
})
// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 110px;
    min-width: 110px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.detailTitle {
  padding-left: 12px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #000;
}

.w350 {
  width: 350px;
}

.w250 {
  width: 250px;
}

.w150 {
  width: 150px;
}

.w200 {
  width: 200px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: #00000080;
  white-space: nowrap;
}

.detailValueDescription {
  font-size: 12px;
  color: #00000080;
}

.detailBox {
  padding-top: 12px;

  .loadingIcon {
    font-size: 30px;
    color: #1890ff;
  }
}

.ant-input-number {
  width: 100%;
}
</style>
