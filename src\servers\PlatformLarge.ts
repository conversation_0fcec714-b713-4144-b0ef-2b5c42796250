import { request } from './request'
// 获取列表
export const GetList = (data) => request({ url: '/api/PlatformCategory/GetList', data })
// 新建
export const Add = (data) => request({ url: '/api/PlatformCategory/Add', data })
// 查看
export const Details = (data) => request({ url: '/api/PlatformCategory/Details', data })
// 编辑
export const Update = (data) => request({ url: '/api/PlatformCategory/Update', data })
// 修改状态
export const UpdateStatus = (data) => request({ url: '/api/PlatformCategory/UpdateStatus', data })
// 删除
export const Delete = (data) => request({ url: '/api/PlatformCategory/Delete', data })
// 日志
export const GetOpLogInfos = (data) => request({ url: '/api/PlatformCategory/GetOpLogInfos', data })
