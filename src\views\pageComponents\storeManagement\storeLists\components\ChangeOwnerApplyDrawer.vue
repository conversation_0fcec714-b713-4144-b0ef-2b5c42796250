<template>
  <a-drawer
    :footer="drawerVisible ? undefined : false"
    v-model:open="drawerVisible"
    width="45vw"
    title="店铺变更负责人申请"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
    @afterOpenChange="formRef.clearValidate()"
    @close="onClose"
  >
    <div class="detailBox">
      <LoadingOutlined v-show="loading" class="loadingIcon" />
      <a-form ref="formRef" :model="editForm" :rules="rules">
        <div class="drawer-title">基础信息</div>
        <a-form-item label="流程类型">
          <span>{{ detail.title }}</span>
        </a-form-item>
        <a-form-item label="流水号">
          <span>--</span>
        </a-form-item>
        <a-form-item label="分部">
          <span>{{ detail.company }}</span>
        </a-form-item>
        <a-form-item label="申请部门">
          <span>{{ detail.creator_department }}</span>
        </a-form-item>
        <a-form-item label="申请人">
          <span>{{ detail.creator }}</span>
        </a-form-item>
        <a-form-item label="申请日期">
          <span>{{ detail.applyDate }}</span>
        </a-form-item>
        <div class="drawer-title">店铺信息</div>
        <a-form-item label="变更店铺">
          <span>{{ detail.shop_name }}</span>
        </a-form-item>
        <a-form-item label="平台大类">
          <span>{{ detail.shop_platform }}</span>
        </a-form-item>
        <a-form-item label="平台小类">
          <span>{{ detail.shop_platform_subtype }}</span>
        </a-form-item>
        <a-form-item label="店铺类型">
          <span>{{ detail.shop_type_name }}</span>
        </a-form-item>
        <a-form-item label="国家/地区">
          <span>{{ detail.shop_country }}</span>
        </a-form-item>
        <a-form-item label="站点">
          <span>{{ detail.shop_site }}</span>
        </a-form-item>
        <a-form-item label="店铺状态">
          <span>{{ detail.shop_status_name }}</span>
        </a-form-item>
        <a-form-item label="营业执照">
          <span>{{ detail.business_license }}</span>
        </a-form-item>
        <a-form-item label="法人">
          <span>{{ detail.legal_rep_name }}</span>
        </a-form-item>
        <a-form-item label="紫鸟企业">
          <span>{{ detail.purple_bird_company }}</span>
        </a-form-item>
        <a-form-item label="紫鸟设备">
          <span>{{ detail.purple_bird_device }}</span>
        </a-form-item>
        <a-form-item label="紫鸟员工账号">
          <span>{{ detail.purple_bird_account || '--' }}</span>
        </a-form-item>
        <a-form-item label="原负责人">
          <span>{{ `${detail.username}/${detail.name}` }}</span>
        </a-form-item>
        <div class="drawer-title">店铺信息</div>
        <a-form-item label="负责人" name="new_owner_id">
          <a-form-item-rest>
            <a-select
              :getPopupContainer="(triggerNode) => triggerNode.parentNode"
              v-model:value="detail.sub_company_id"
              placeholder="请选择所属公司"
              @change="changeCompany"
              class="short"
              show-search
              :filter-option="(input, option) => filterOption(input, option)"
              disabled
            >
              <a-select-option v-for="item in companyList" :key="item.value" :value="item.value" :label="item.label">{{ item.label }}</a-select-option>
            </a-select>
            <a-tree-select
              show-search
              class="short"
              v-model:value="detail.department_id"
              allow-clear
              :tree-data="departmentList"
              :fieldNames="{
                children: 'childrenList',
                label: 'departmentname',
                value: 'departmentid',
              }"
              :maxTagCount="1"
              :listHeight="400"
              :dropdownMatchSelectWidth="250"
              placeholder="请选择所在部门"
              @click="onClickDepartment"
              @change="changeDepartment"
              treeNodeFilterProp="departmentname"
              :filter-option="(input, option) => filterOption(input, option)"
            />
          </a-form-item-rest>
          <a-select
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="editForm.new_owner_id"
            placeholder="请选择负责人"
            @click="onClickPrincipal"
            class="short"
            show-search
            :filter-option="(input, option) => filterOption(input, option)"
          >
            <a-select-option v-for="item in userList" :key="item.id" :value="item.id" :label="item.principal">{{ item.principal }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="变更原因" name="reason">
          <a-textarea v-model:value="editForm.reason" :rows="4" :maxlength="500" :showCount="true" />
        </a-form-item>
      </a-form>
    </div>
    <template #footer>
      <a-button style="margin-right: 0.8333rem" type="primary" @click="onSubmit">提交</a-button>
      <a-button @click="onClose">取消</a-button>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { LoadingOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import { ChangeOwnerApply, Details } from '@/servers/StoreLists'
import { UserInfo, GetUserInfoById } from '@/servers/User'
import { filterOption } from '@/utils/index'
import { GetDepartmentTreeList, GetCompanyList, GetUserOptions } from '@/servers/UserManager'

const emit = defineEmits(['updateStatus', 'success'])
const drawerVisible = ref(false)
const loading = ref(false)
const formRef = ref()
const editForm = ref<any>({
  shop_id: '',
  new_owner_id: null,
  reason: '',
})
const detail = ref<any>({})
const companyList = ref<any>([])
const departmentList = ref<any>([])
const userList = ref<any>([])
const rules: Record<string, Rule[]> = {
  new_owner_id: [{ required: true, message: '请选择负责人', trigger: ['change', 'blur'] }],
  reason: [{ required: true, message: '请输入变更原因', trigger: ['change', 'blur'] }],
}

const onSubmit = async () => {
  try {
    await formRef.value.validateFields()
    submit()
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
const submit = () => {
  const obj = JSON.parse(JSON.stringify(editForm.value))
  ChangeOwnerApply(obj).then(() => {
    message.success('店铺变更负责人流程已提交，请耐心等待完成！')
    onClose()
    emit('success')
  })
}

const onClickDepartment = () => {
  if (!detail.value.sub_company_id) {
    message.info('请先选择所属公司')
  }
}
// 获取公司树状下拉框
const getCompanyList = () => {
  GetCompanyList().then((res) => {
    res.data.forEach((x) => {
      x.label = x.company_name
      x.value = x.company_id
    })
    companyList.value = res.data
  })
}

// 获取部门树状下拉框(内部)
const getDepartmentTreeList = () => {
  if (!detail.value.sub_company_id) {
    return
  }
  GetDepartmentTreeList({ subcompanyid1: detail.value.sub_company_id }).then((res) => {
    departmentList.value = res.data
  })
}

// 获取用户下拉数据
const getUserList = async () => {
  const res = await GetUserOptions({ subcompanyid1: detail.value.sub_company_id, departmentid: detail.value.department_id })
  userList.value = res.data
}

const onClickPrincipal = () => {
  if (!detail.value.department_id) {
    message.info('请先选择所在部门')
  }
}

const changeCompany = () => {
  getDepartmentTreeList()
  detail.value.department_id = ''
  editForm.value.new_owner_id = ''
  userList.value = []
}

const changeDepartment = async () => {
  if (!detail.value.department_id) {
    return
  }
  editForm.value.new_owner_id = ''
  getUserList()
}

const onClose = () => {
  formRef.value.resetFields()
  drawerVisible.value = false
}
const open = async (id) => {
  drawerVisible.value = true
  loading.value = true
  editForm.value = {}
  const res = await Details({ shop_id: id })
  detail.value = res.data
  editForm.value.shop_id = detail.value.id
  loading.value = false

  const userInfo = (await UserInfo()).data
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const applyDate = `${year}-${month}-${day}`
  detail.value.applyDate = applyDate

  const oldOwnerInfo = (await GetUserInfoById({ user_id: detail.value.owner_id })).data
  detail.value.purple_bird_account = oldOwnerInfo.external_system_account?.purple_bird_account
    .map((n) => {
      return n.account_number
    })
    .toString()
    .replaceAll(',', '，')
  detail.value.purple_bird_account = detail.value.purple_bird_account.replaceAll(',', '，')
  detail.value.creator = `${userInfo.user_name}/${userInfo.real_name}`
  detail.value.company = userInfo.company
  detail.value.creator_department = userInfo.department
  detail.value.title = `店铺变更负责人流程-${userInfo.real_name}-${applyDate}`
  getDepartmentTreeList()
  getUserList()
}
onMounted(() => {
  getCompanyList()
})
// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 110px;
    min-width: 110px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.detailTitle {
  padding-left: 12px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #000;
}

.w350 {
  width: 350px;
}

.w250 {
  width: 250px;
}

.w150 {
  width: 150px;
}

.w200 {
  width: 200px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: #00000080;
  white-space: nowrap;
}

.detailValueDescription {
  font-size: 12px;
  color: #00000080;
}

.detailBox {
  padding-top: 12px;

  .loadingIcon {
    font-size: 30px;
    color: #1890ff;
  }
}

.ant-input-number {
  width: 100%;
}

.short {
  width: 15.8rem;
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
